# AirMonitor 组件库设计规范

## 1. 基础组件设计

### 1.1 按钮组件 (Button)
**主要按钮 (Primary Button)：**
```css
/* 明亮主题 */
background: #0078D4;
color: #FFFFFF;
border: none;
border-radius: 4px;
padding: 8px 16px;
font: 14px/20px Segoe UI, semibold;
box-shadow: 0 1px 2px rgba(0,0,0,0.133);

/* 悬停状态 */
background: #106EBE;
box-shadow: 0 2px 4px rgba(0,0,0,0.133);

/* 按下状态 */
background: #005A9E;
box-shadow: 0 1px 2px rgba(0,0,0,0.133);

/* 禁用状态 */
background: #C8C6C4;
color: #A19F9D;
```

**次要按钮 (Secondary Button)：**
```css
/* 明亮主题 */
background: transparent;
color: #0078D4;
border: 1px solid #0078D4;
border-radius: 4px;
padding: 7px 15px; /* 减1px补偿边框 */
font: 14px/20px Segoe UI, semibold;

/* 悬停状态 */
background: #F3F2F1;
border-color: #106EBE;
color: #106EBE;

/* 按下状态 */
background: #EDEBE9;
border-color: #005A9E;
color: #005A9E;
```

**危险按钮 (Danger Button)：**
```css
/* 明亮主题 */
background: #D13438;
color: #FFFFFF;
border: none;
border-radius: 4px;
padding: 8px 16px;
font: 14px/20px Segoe UI, semibold;

/* 悬停状态 */
background: #B02E32;
/* 按下状态 */
background: #8F262A;
```

### 1.2 输入框组件 (Input)
**标准输入框：**
```css
/* 明亮主题 */
background: #FFFFFF;
border: 1px solid #8A8886;
border-radius: 2px;
padding: 8px 12px;
font: 14px/20px Segoe UI;
color: #323130;

/* 焦点状态 */
border-color: #0078D4;
box-shadow: 0 0 0 1px #0078D4;

/* 错误状态 */
border-color: #D13438;
box-shadow: 0 0 0 1px #D13438;

/* 禁用状态 */
background: #F3F2F1;
border-color: #C8C6C4;
color: #A19F9D;
```

**标签设计：**
```css
/* 输入框标签 */
font: 12px/16px Segoe UI, semibold;
color: #323130;
margin-bottom: 4px;
display: block;

/* 必填标识 */
.required::after {
    content: " *";
    color: #D13438;
}
```

### 1.3 下拉选择组件 (Select)
**下拉框设计：**
```css
/* 主体样式 */
background: #FFFFFF;
border: 1px solid #8A8886;
border-radius: 2px;
padding: 8px 32px 8px 12px;
font: 14px/20px Segoe UI;
color: #323130;
position: relative;

/* 下拉箭头 */
.select-arrow {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    background-image: url('chevron-down.svg');
}

/* 下拉选项 */
.select-options {
    background: #FFFFFF;
    border: 1px solid #8A8886;
    border-radius: 2px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.133);
    max-height: 200px;
    overflow-y: auto;
}

.select-option {
    padding: 8px 12px;
    font: 14px/20px Segoe UI;
    color: #323130;
    cursor: pointer;
}

.select-option:hover {
    background: #F3F2F1;
}

.select-option.selected {
    background: #0078D4;
    color: #FFFFFF;
}
```

### 1.4 复选框和单选按钮
**复选框 (Checkbox)：**
```css
/* 复选框容器 */
.checkbox {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

/* 复选框样式 */
.checkbox-input {
    width: 16px;
    height: 16px;
    border: 1px solid #8A8886;
    border-radius: 2px;
    background: #FFFFFF;
    position: relative;
}

/* 选中状态 */
.checkbox-input.checked {
    background: #0078D4;
    border-color: #0078D4;
}

.checkbox-input.checked::after {
    content: '';
    position: absolute;
    left: 4px;
    top: 1px;
    width: 6px;
    height: 10px;
    border: 2px solid #FFFFFF;
    border-left: none;
    border-top: none;
    transform: rotate(45deg);
}

/* 标签文字 */
.checkbox-label {
    font: 14px/20px Segoe UI;
    color: #323130;
    user-select: none;
}
```

**单选按钮 (Radio)：**
```css
/* 单选按钮样式 */
.radio-input {
    width: 16px;
    height: 16px;
    border: 1px solid #8A8886;
    border-radius: 50%;
    background: #FFFFFF;
    position: relative;
}

/* 选中状态 */
.radio-input.checked {
    border-color: #0078D4;
}

.radio-input.checked::after {
    content: '';
    position: absolute;
    left: 3px;
    top: 3px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #0078D4;
}
```

## 2. 导航组件设计

### 2.1 主导航栏 (NavigationView)
**导航容器：**
```css
/* 导航栏主体 */
.navigation-view {
    width: 320px;
    height: 100vh;
    background: #F3F2F1;
    border-right: 1px solid #EDEBE9;
    display: flex;
    flex-direction: column;
    transition: width 250ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

/* 折叠状态 */
.navigation-view.collapsed {
    width: 48px;
}

/* 导航头部 */
.navigation-header {
    height: 48px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #EDEBE9;
}

/* 导航菜单 */
.navigation-menu {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
}
```

**导航项设计：**
```css
/* 导航项 */
.navigation-item {
    height: 40px;
    margin: 0 8px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    padding: 0 12px;
    gap: 12px;
    cursor: pointer;
    transition: background 150ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

/* 悬停状态 */
.navigation-item:hover {
    background: #E1DFDD;
}

/* 选中状态 */
.navigation-item.selected {
    background: #0078D4;
    color: #FFFFFF;
}

.navigation-item.selected .navigation-icon {
    color: #FFFFFF;
}

/* 图标样式 */
.navigation-icon {
    width: 16px;
    height: 16px;
    color: #605E5C;
}

/* 文字样式 */
.navigation-text {
    font: 14px/20px Segoe UI;
    color: #323130;
    white-space: nowrap;
    overflow: hidden;
}

/* 折叠状态下隐藏文字 */
.navigation-view.collapsed .navigation-text {
    display: none;
}
```

### 2.2 面包屑导航 (Breadcrumb)
```css
/* 面包屑容器 */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 0;
    font: 12px/16px Segoe UI;
}

/* 面包屑项 */
.breadcrumb-item {
    color: #605E5C;
    text-decoration: none;
    cursor: pointer;
}

.breadcrumb-item:hover {
    color: #0078D4;
    text-decoration: underline;
}

.breadcrumb-item.current {
    color: #323130;
    cursor: default;
}

.breadcrumb-item.current:hover {
    text-decoration: none;
}

/* 分隔符 */
.breadcrumb-separator {
    color: #8A8886;
    font-size: 10px;
}
```

### 2.3 标签页导航 (Tabs)
```css
/* 标签页容器 */
.tabs {
    border-bottom: 1px solid #EDEBE9;
    display: flex;
    align-items: center;
}

/* 标签页项 */
.tab-item {
    height: 40px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    font: 14px/20px Segoe UI;
    color: #605E5C;
    transition: all 150ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

/* 悬停状态 */
.tab-item:hover {
    color: #323130;
    background: #F3F2F1;
}

/* 选中状态 */
.tab-item.active {
    color: #0078D4;
    border-bottom-color: #0078D4;
}

/* 关闭按钮 */
.tab-close {
    width: 16px;
    height: 16px;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 150ms;
}

.tab-item:hover .tab-close {
    opacity: 1;
}

.tab-close:hover {
    background: #E1DFDD;
}
```

## 3. 数据展示组件

### 3.1 表格组件 (Table)
```css
/* 表格容器 */
.table-container {
    background: #FFFFFF;
    border: 1px solid #EDEBE9;
    border-radius: 4px;
    overflow: hidden;
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
    font: 14px/20px Segoe UI;
}

/* 表头样式 */
.table-header {
    background: #F3F2F1;
    border-bottom: 1px solid #EDEBE9;
}

.table-header th {
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    color: #323130;
    border-right: 1px solid #EDEBE9;
}

.table-header th:last-child {
    border-right: none;
}

/* 表格行样式 */
.table-row {
    border-bottom: 1px solid #F3F2F1;
    transition: background 150ms;
}

.table-row:hover {
    background: #FAF9F8;
}

.table-row.selected {
    background: #E3F2FD;
}

/* 表格单元格 */
.table-cell {
    padding: 12px 16px;
    color: #323130;
    border-right: 1px solid #F3F2F1;
}

.table-cell:last-child {
    border-right: none;
}

/* 数据类型样式 */
.table-cell.number {
    text-align: right;
    font-family: Consolas, monospace;
}

.table-cell.status {
    display: flex;
    align-items: center;
    gap: 8px;
}
```

### 3.2 卡片组件 (Card)
```css
/* 基础卡片 */
.card {
    background: #FFFFFF;
    border: 1px solid #EDEBE9;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.133);
    overflow: hidden;
    transition: box-shadow 250ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

.card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.133);
}

/* 卡片头部 */
.card-header {
    padding: 16px 24px;
    border-bottom: 1px solid #F3F2F1;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-title {
    font: 16px/24px Segoe UI, semibold;
    color: #323130;
    margin: 0;
}

/* 卡片内容 */
.card-content {
    padding: 24px;
}

/* 卡片底部 */
.card-footer {
    padding: 16px 24px;
    border-top: 1px solid #F3F2F1;
    background: #FAF9F8;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
}

/* 状态卡片变体 */
.card.success {
    border-left: 4px solid #107C10;
}

.card.warning {
    border-left: 4px solid #FFB900;
}

.card.error {
    border-left: 4px solid #D13438;
}

.card.info {
    border-left: 4px solid #0078D4;
}
```

### 3.3 状态指示器组件
**状态徽章 (Badge)：**
```css
/* 基础徽章 */
.badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 12px;
    font: 12px/16px Segoe UI, semibold;
    white-space: nowrap;
}

/* 状态变体 */
.badge.success {
    background: #DFF6DD;
    color: #107C10;
}

.badge.warning {
    background: #FFF4CE;
    color: #8A6914;
}

.badge.error {
    background: #FDE7E9;
    color: #A4262C;
}

.badge.info {
    background: #E3F2FD;
    color: #0078D4;
}

.badge.neutral {
    background: #F3F2F1;
    color: #605E5C;
}
```

**进度条 (Progress)：**
```css
/* 进度条容器 */
.progress {
    width: 100%;
    height: 4px;
    background: #F3F2F1;
    border-radius: 2px;
    overflow: hidden;
}

/* 进度条填充 */
.progress-fill {
    height: 100%;
    background: #0078D4;
    border-radius: 2px;
    transition: width 250ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

/* 进度条变体 */
.progress-fill.success {
    background: #107C10;
}

.progress-fill.warning {
    background: #FFB900;
}

.progress-fill.error {
    background: #D13438;
}

/* 不确定进度条 */
.progress.indeterminate .progress-fill {
    width: 30%;
    animation: progress-indeterminate 2s infinite linear;
}

@keyframes progress-indeterminate {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(333%); }
}
```

## 4. 反馈组件设计

### 4.1 消息提示 (Message)
```css
/* 消息容器 */
.message {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    border-radius: 4px;
    border-left: 4px solid;
    margin-bottom: 16px;
    font: 14px/20px Segoe UI;
}

/* 消息图标 */
.message-icon {
    width: 16px;
    height: 16px;
    margin-top: 2px;
    flex-shrink: 0;
}

/* 消息内容 */
.message-content {
    flex: 1;
}

.message-title {
    font-weight: 600;
    margin-bottom: 4px;
}

.message-description {
    color: #605E5C;
}

/* 关闭按钮 */
.message-close {
    width: 16px;
    height: 16px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 150ms;
}

.message-close:hover {
    opacity: 1;
}

/* 消息类型变体 */
.message.success {
    background: #DFF6DD;
    border-color: #107C10;
    color: #107C10;
}

.message.warning {
    background: #FFF4CE;
    border-color: #FFB900;
    color: #8A6914;
}

.message.error {
    background: #FDE7E9;
    border-color: #D13438;
    color: #A4262C;
}

.message.info {
    background: #E3F2FD;
    border-color: #0078D4;
    color: #0078D4;
}
```

### 4.2 加载状态组件
**加载指示器 (Spinner)：**
```css
/* 加载指示器 */
.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #F3F2F1;
    border-top: 2px solid #0078D4;
    border-radius: 50%;
    animation: spinner-rotate 1s linear infinite;
}

@keyframes spinner-rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 大小变体 */
.spinner.small {
    width: 16px;
    height: 16px;
}

.spinner.large {
    width: 32px;
    height: 32px;
    border-width: 3px;
}

/* 加载状态覆盖层 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 16px;
    z-index: 1000;
}

.loading-text {
    font: 14px/20px Segoe UI;
    color: #605E5C;
}
```

## 5. 暗色主题适配

### 5.1 组件暗色主题样式
**按钮暗色主题：**
```css
/* 暗色主题按钮 */
[data-theme="dark"] .button.primary {
    background: #4CC2FF;
    color: #000000;
}

[data-theme="dark"] .button.primary:hover {
    background: #6BD3FF;
}

[data-theme="dark"] .button.secondary {
    color: #4CC2FF;
    border-color: #4CC2FF;
}

[data-theme="dark"] .button.secondary:hover {
    background: #2D2D2D;
    border-color: #6BD3FF;
    color: #6BD3FF;
}
```

**输入框暗色主题：**
```css
[data-theme="dark"] .input {
    background: #2D2D2D;
    border-color: #484848;
    color: #FFFFFF;
}

[data-theme="dark"] .input:focus {
    border-color: #4CC2FF;
    box-shadow: 0 0 0 1px #4CC2FF;
}
```

**卡片暗色主题：**
```css
[data-theme="dark"] .card {
    background: #2D2D2D;
    border-color: #484848;
    box-shadow: 0 2px 4px rgba(0,0,0,0.4);
}

[data-theme="dark"] .card-header {
    border-bottom-color: #484848;
}

[data-theme="dark"] .card-title {
    color: #FFFFFF;
}

[data-theme="dark"] .card-footer {
    background: #3A3A3A;
    border-top-color: #484848;
}
```
