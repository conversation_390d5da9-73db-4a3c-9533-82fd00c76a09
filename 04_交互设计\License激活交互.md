# AirMonitor License激活交互设计

## 1. License激活流程交互

### 1.1 激活向导界面
**激活向导容器：**
```css
/* License激活主容器 */
.license-activation {
    min-height: 100vh;
    background: linear-gradient(135deg, #0078D4 0%, #6B69D6 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24px;
    position: relative;
    overflow: hidden;
}

/* 背景装饰元素 */
.license-activation::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: background-float 20s linear infinite;
}

@keyframes background-float {
    0% { transform: translate(0, 0) rotate(0deg); }
    100% { transform: translate(-50px, -50px) rotate(360deg); }
}

/* 激活卡片 */
.activation-card {
    background: var(--color-background);
    border-radius: 12px;
    box-shadow: 0 16px 32px rgba(0,0,0,0.2);
    width: 100%;
    max-width: 480px;
    overflow: hidden;
    position: relative;
    z-index: 1;
    animation: card-enter 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
}

@keyframes card-enter {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 激活头部 */
.activation-header {
    padding: 32px 32px 24px;
    text-align: center;
    background: linear-gradient(135deg, #FAF9F8 0%, #F3F2F1 100%);
    position: relative;
}

.activation-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #0078D4, #6B69D6);
    transform: translateX(-50%);
    border-radius: 2px;
}

.activation-logo {
    width: 64px;
    height: 64px;
    margin: 0 auto 16px;
    background: linear-gradient(135deg, #0078D4, #6B69D6);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFFFFF;
    font-size: 24px;
    animation: logo-pulse 2s infinite;
}

@keyframes logo-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.activation-title {
    font: 24px/32px Segoe UI, semibold;
    color: var(--color-text-primary);
    margin: 0 0 8px;
}

.activation-subtitle {
    font: 14px/20px Segoe UI;
    color: var(--color-text-secondary);
    margin: 0;
}
```

### 1.2 激活步骤交互
**步骤指示器：**
```css
/* 激活步骤指示器 */
.activation-steps {
    padding: 24px 32px 16px;
    background: var(--color-background);
}

.steps-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    margin-bottom: 24px;
}

.step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    position: relative;
    flex: 1;
    max-width: 80px;
}

.step-item:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 16px;
    right: -50%;
    width: 100%;
    height: 2px;
    background: var(--color-border);
    z-index: 1;
}

.step-item.completed::after {
    background: var(--color-success);
}

.step-item.active::after {
    background: linear-gradient(to right, var(--color-primary) 50%, var(--color-border) 50%);
}

/* 步骤图标 */
.step-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--color-surface);
    color: var(--color-text-tertiary);
    font-size: 14px;
    transition: all 300ms cubic-bezier(0.4, 0.0, 0.2, 1);
    position: relative;
    z-index: 2;
}

.step-item.active .step-icon {
    background: var(--color-primary);
    color: #FFFFFF;
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 120, 212, 0.3);
}

.step-item.completed .step-icon {
    background: var(--color-success);
    color: #FFFFFF;
}

.step-item.completed .step-icon::before {
    content: '✓';
    font-size: 16px;
    font-weight: bold;
}

.step-item.error .step-icon {
    background: var(--color-error);
    color: #FFFFFF;
    animation: step-error-shake 0.5s ease-in-out;
}

@keyframes step-error-shake {
    0%, 20%, 40%, 60%, 80% { transform: translateX(-3px); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(3px); }
    100% { transform: translateX(0); }
}

/* 步骤标签 */
.step-label {
    font: 11px/14px Segoe UI, semibold;
    color: var(--color-text-secondary);
    text-align: center;
    transition: color 150ms;
}

.step-item.active .step-label {
    color: var(--color-primary);
}

.step-item.completed .step-label {
    color: var(--color-success);
}

.step-item.error .step-label {
    color: var(--color-error);
}
```

### 1.3 License文件导入交互
**文件拖拽上传：**
```css
/* License文件导入区域 */
.license-import {
    padding: 24px 32px;
    background: var(--color-background);
}

.import-zone {
    border: 2px dashed var(--color-border);
    border-radius: 8px;
    padding: 32px 24px;
    text-align: center;
    transition: all 250ms cubic-bezier(0.4, 0.0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.import-zone:hover {
    border-color: var(--color-primary);
    background: rgba(0, 120, 212, 0.05);
}

.import-zone.dragover {
    border-color: var(--color-primary);
    background: rgba(0, 120, 212, 0.1);
    transform: scale(1.02);
}

.import-zone.uploading {
    border-color: var(--color-warning);
    background: rgba(255, 185, 0, 0.05);
    pointer-events: none;
}

.import-zone.success {
    border-color: var(--color-success);
    background: rgba(16, 124, 16, 0.05);
    animation: import-success 0.6s ease-out;
}

.import-zone.error {
    border-color: var(--color-error);
    background: rgba(209, 52, 56, 0.05);
    animation: import-error 0.5s ease-out;
}

@keyframes import-success {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes import-error {
    0%, 20%, 40%, 60%, 80% { transform: translateX(-5px); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(5px); }
    100% { transform: translateX(0); }
}

/* 导入图标 */
.import-icon {
    width: 48px;
    height: 48px;
    margin: 0 auto 16px;
    background: var(--color-surface);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-text-secondary);
    font-size: 20px;
    transition: all 250ms;
}

.import-zone:hover .import-icon {
    background: var(--color-primary);
    color: #FFFFFF;
    transform: scale(1.1);
}

.import-zone.uploading .import-icon {
    background: var(--color-warning);
    color: #FFFFFF;
    animation: import-uploading 1s linear infinite;
}

@keyframes import-uploading {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 导入文本 */
.import-text {
    margin-bottom: 8px;
}

.import-title {
    font: 16px/24px Segoe UI, semibold;
    color: var(--color-text-primary);
    margin-bottom: 4px;
}

.import-description {
    font: 14px/20px Segoe UI;
    color: var(--color-text-secondary);
}

.import-hint {
    font: 12px/16px Segoe UI;
    color: var(--color-text-tertiary);
    margin-top: 8px;
}

/* 文件选择按钮 */
.import-button {
    background: var(--color-primary);
    color: #FFFFFF;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font: 14px/20px Segoe UI, semibold;
    cursor: pointer;
    transition: all 150ms;
    margin-top: 16px;
}

.import-button:hover {
    background: var(--color-primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 120, 212, 0.3);
}

/* 上传进度 */
.upload-progress {
    margin-top: 16px;
    opacity: 0;
    transition: opacity 250ms;
}

.import-zone.uploading .upload-progress {
    opacity: 1;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: var(--color-surface);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: var(--color-primary);
    border-radius: 2px;
    transition: width 250ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

.progress-text {
    font: 12px/16px Segoe UI;
    color: var(--color-text-secondary);
    text-align: center;
}
```

## 2. 设备指纹显示交互

### 2.1 设备指纹生成动画
**指纹生成过程：**
```css
/* 设备指纹容器 */
.device-fingerprint {
    padding: 24px 32px;
    background: var(--color-background);
}

.fingerprint-section {
    margin-bottom: 24px;
}

.fingerprint-title {
    font: 16px/24px Segoe UI, semibold;
    color: var(--color-text-primary);
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.fingerprint-icon {
    width: 16px;
    height: 16px;
    color: var(--color-primary);
}

/* 指纹生成状态 */
.fingerprint-status {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: var(--color-surface);
    border-radius: 6px;
    margin-bottom: 16px;
}

.status-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
}

.status-icon.generating {
    color: var(--color-warning);
    animation: fingerprint-generating 1.5s linear infinite;
}

.status-icon.success {
    color: var(--color-success);
    animation: fingerprint-success 0.6s ease-out;
}

.status-icon.error {
    color: var(--color-error);
    animation: fingerprint-error 0.5s ease-out;
}

@keyframes fingerprint-generating {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes fingerprint-success {
    0% { transform: scale(0.8); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

@keyframes fingerprint-error {
    0%, 20%, 40%, 60%, 80% { transform: translateX(-3px); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(3px); }
    100% { transform: translateX(0); }
}

.status-text {
    flex: 1;
}

.status-message {
    font: 14px/20px Segoe UI, semibold;
    color: var(--color-text-primary);
    margin-bottom: 2px;
}

.status-description {
    font: 12px/16px Segoe UI;
    color: var(--color-text-secondary);
}

/* 硬件信息收集进度 */
.hardware-collection {
    margin-bottom: 16px;
}

.collection-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
    transition: all 150ms;
}

.collection-icon {
    width: 16px;
    height: 16px;
    color: var(--color-text-tertiary);
    transition: color 150ms;
}

.collection-item.collecting .collection-icon {
    color: var(--color-warning);
    animation: collection-pulse 1s infinite;
}

.collection-item.completed .collection-icon {
    color: var(--color-success);
}

.collection-item.error .collection-icon {
    color: var(--color-error);
}

@keyframes collection-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.collection-label {
    flex: 1;
    font: 13px/18px Segoe UI;
    color: var(--color-text-secondary);
}

.collection-item.collecting .collection-label {
    color: var(--color-text-primary);
    font-weight: 600;
}

.collection-status {
    font: 11px/14px Segoe UI, semibold;
    padding: 2px 6px;
    border-radius: 2px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.collection-status.collecting {
    background: rgba(255, 185, 0, 0.1);
    color: var(--color-warning);
}

.collection-status.completed {
    background: rgba(16, 124, 16, 0.1);
    color: var(--color-success);
}

.collection-status.error {
    background: rgba(209, 52, 56, 0.1);
    color: var(--color-error);
}
```

### 2.2 指纹显示和复制
**指纹显示区域：**
```css
/* 设备指纹显示 */
.fingerprint-display {
    background: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 6px;
    padding: 16px;
    position: relative;
}

.fingerprint-value {
    font: 14px/20px Consolas, monospace;
    color: var(--color-text-primary);
    word-break: break-all;
    line-height: 1.6;
    margin-bottom: 12px;
    padding: 12px;
    background: var(--color-background);
    border-radius: 4px;
    border: 1px solid var(--color-border);
    position: relative;
}

/* 指纹分段显示 */
.fingerprint-segment {
    display: inline-block;
    margin-right: 8px;
    padding: 2px 4px;
    background: rgba(0, 120, 212, 0.1);
    border-radius: 2px;
    transition: background 150ms;
}

.fingerprint-segment:hover {
    background: rgba(0, 120, 212, 0.2);
}

/* 复制按钮 */
.fingerprint-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.copy-button {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: var(--color-primary);
    color: #FFFFFF;
    border: none;
    border-radius: 4px;
    font: 12px/16px Segoe UI, semibold;
    cursor: pointer;
    transition: all 150ms;
}

.copy-button:hover {
    background: var(--color-primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 120, 212, 0.3);
}

.copy-button.copied {
    background: var(--color-success);
    animation: copy-success 0.3s ease-out;
}

@keyframes copy-success {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.copy-icon {
    width: 12px;
    height: 12px;
}

/* 复制成功提示 */
.copy-tooltip {
    position: absolute;
    top: -30px;
    right: 0;
    background: var(--color-text-primary);
    color: var(--color-background);
    padding: 4px 8px;
    border-radius: 2px;
    font: 11px/14px Segoe UI;
    white-space: nowrap;
    opacity: 0;
    transform: translateY(5px);
    transition: all 150ms;
    pointer-events: none;
}

.copy-tooltip.show {
    opacity: 1;
    transform: translateY(0);
}

.copy-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    right: 8px;
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid var(--color-text-primary);
}
```

## 3. 激活验证交互

### 3.1 验证过程动画
**验证状态指示：**
```css
/* 验证过程容器 */
.verification-process {
    padding: 24px 32px;
    background: var(--color-background);
    text-align: center;
}

.verification-status {
    margin-bottom: 24px;
}

/* 验证图标 */
.verification-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    transition: all 300ms cubic-bezier(0.4, 0.0, 0.2, 1);
    position: relative;
}

.verification-icon.verifying {
    background: linear-gradient(135deg, #0078D4, #6B69D6);
    color: #FFFFFF;
    animation: verification-pulse 2s infinite;
}

.verification-icon.success {
    background: var(--color-success);
    color: #FFFFFF;
    animation: verification-success 0.8s ease-out;
}

.verification-icon.error {
    background: var(--color-error);
    color: #FFFFFF;
    animation: verification-error 0.6s ease-out;
}

@keyframes verification-pulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(0, 120, 212, 0.4);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 20px rgba(0, 120, 212, 0);
    }
}

@keyframes verification-success {
    0% { transform: scale(0.8); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

@keyframes verification-error {
    0%, 20%, 40%, 60%, 80% { transform: translateX(-5px); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(5px); }
    100% { transform: translateX(0); }
}

/* 验证进度环 */
.verification-icon.verifying::after {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 2px solid transparent;
    border-top: 2px solid rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    animation: verification-spin 1s linear infinite;
}

@keyframes verification-spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 验证消息 */
.verification-message {
    font: 18px/26px Segoe UI, semibold;
    color: var(--color-text-primary);
    margin-bottom: 8px;
}

.verification-description {
    font: 14px/20px Segoe UI;
    color: var(--color-text-secondary);
    margin-bottom: 24px;
    max-width: 300px;
    margin-left: auto;
    margin-right: auto;
}

/* 验证步骤 */
.verification-steps {
    display: flex;
    justify-content: center;
    gap: 24px;
    margin-bottom: 24px;
}

.verification-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    min-width: 100px;
}

.step-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--color-surface);
    color: var(--color-text-tertiary);
    font-size: 16px;
    transition: all 250ms;
}

.verification-step.active .step-circle {
    background: var(--color-primary);
    color: #FFFFFF;
    animation: step-active-pulse 1.5s infinite;
}

.verification-step.completed .step-circle {
    background: var(--color-success);
    color: #FFFFFF;
}

.verification-step.error .step-circle {
    background: var(--color-error);
    color: #FFFFFF;
}

@keyframes step-active-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.step-title {
    font: 12px/16px Segoe UI, semibold;
    color: var(--color-text-secondary);
    text-align: center;
}

.verification-step.active .step-title {
    color: var(--color-primary);
}

.verification-step.completed .step-title {
    color: var(--color-success);
}

.verification-step.error .step-title {
    color: var(--color-error);
}
```