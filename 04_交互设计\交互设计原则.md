# AirMonitor 交互设计原则

## 1. 交互设计核心原则

### 1.1 Windows平台原生性
**Fluent Design交互模式：**
- **深度和层次**：通过阴影、模糊、透明度创建视觉层次
- **运动和动画**：自然流畅的过渡动画，增强空间感知
- **材质和光线**：模拟真实材质的交互反馈
- **缩放和适应**：支持不同屏幕尺寸和输入方式

**Windows交互习惯：**
- **右键上下文菜单**：所有可操作元素支持右键菜单
- **键盘导航**：完整的Tab键导航和快捷键支持
- **拖拽操作**：文件、数据、界面元素的拖拽支持
- **多选操作**：Ctrl+点击、Shift+点击的多选模式

### 1.2 专业工具交互特色
**效率优先原则：**
- **快捷操作**：常用功能提供多种访问路径
- **批量处理**：支持批量选择和批量操作
- **状态记忆**：记住用户的操作习惯和界面状态
- **专家模式**：为熟练用户提供高效的操作方式

**安全可靠原则：**
- **操作确认**：危险操作必须二次确认
- **撤销机制**：支持操作撤销和状态恢复
- **错误预防**：通过界面设计预防用户错误
- **渐进披露**：复杂功能分步骤引导

### 1.3 离线软件交互适配
**License权限交互：**
- **权限可视化**：清晰显示用户的权限范围
- **功能引导**：权限不足时提供升级引导
- **状态反馈**：License状态的实时反馈
- **激活流程**：简化的License激活交互

**离线体验优化：**
- **本地响应**：所有交互提供即时反馈
- **数据缓存**：智能的数据缓存和预加载
- **离线提示**：网络状态的清晰指示
- **同步机制**：数据同步状态的可视化

## 2. 基础交互规范

### 2.1 鼠标交互规范
**点击交互：**
- **单击**：选择、激活、执行操作
- **双击**：打开、编辑、快速操作
- **右键**：显示上下文菜单
- **中键**：滚动、平移、缩放

**鼠标状态反馈：**
```css
/* 可点击元素 */
.clickable {
    cursor: pointer;
    transition: all 150ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

.clickable:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.133);
}

.clickable:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.133);
}

/* 拖拽元素 */
.draggable {
    cursor: grab;
}

.draggable:active {
    cursor: grabbing;
}

/* 禁用元素 */
.disabled {
    cursor: not-allowed;
    opacity: 0.6;
    pointer-events: none;
}

/* 加载状态 */
.loading {
    cursor: wait;
}

/* 文本选择 */
.text-selectable {
    cursor: text;
    user-select: text;
}

/* 调整大小 */
.resizable-horizontal {
    cursor: ew-resize;
}

.resizable-vertical {
    cursor: ns-resize;
}

.resizable-diagonal {
    cursor: nw-resize;
}
```

### 2.2 键盘交互规范
**基础键盘导航：**
- **Tab键**：在可交互元素间正向导航
- **Shift+Tab**：在可交互元素间反向导航
- **Enter键**：激活当前焦点元素
- **Esc键**：取消操作、关闭弹窗
- **空格键**：选择/取消选择、滚动页面

**方向键导航：**
- **上下箭头**：列表、菜单、表格行间导航
- **左右箭头**：标签页、表格列间导航
- **Home/End**：跳转到开始/结束位置
- **Page Up/Down**：页面滚动

**功能快捷键：**
```javascript
// 全局快捷键定义
const globalShortcuts = {
    'Ctrl+N': 'newConnection',      // 新建连接
    'Ctrl+O': 'openFile',           // 打开文件
    'Ctrl+S': 'saveData',           // 保存数据
    'Ctrl+Z': 'undo',               // 撤销操作
    'Ctrl+Y': 'redo',               // 重做操作
    'Ctrl+F': 'search',             // 搜索功能
    'Ctrl+R': 'refresh',            // 刷新数据
    'F1': 'help',                   // 帮助文档
    'F5': 'refresh',                // 刷新页面
    'F11': 'fullscreen',            // 全屏切换
    'Alt+F4': 'closeApp',           // 关闭应用
    'Ctrl+,': 'settings',           // 打开设置
    'Ctrl+Shift+I': 'devTools'     // 开发者工具
};

// 模块特定快捷键
const moduleShortcuts = {
    'monitoring': {
        'Ctrl+1': 'switchToRealtime',   // 切换到实时监控
        'Ctrl+2': 'switchToHistory',    // 切换到历史数据
        'Ctrl+3': 'switchToAlarms',     // 切换到报警信息
        'Space': 'pauseResume'          // 暂停/恢复监控
    },
    'control': {
        'Ctrl+E': 'emergencyStop',      // 紧急停止
        'Ctrl+T': 'testMode',           // 测试模式
        'Ctrl+M': 'manualMode'          // 手动模式
    },
    'analysis': {
        'Ctrl+G': 'generateChart',      // 生成图表
        'Ctrl+E': 'exportData',         // 导出数据
        'Ctrl+P': 'printReport'         // 打印报告
    }
};
```

### 2.3 触控交互支持
**基础触控手势：**
- **点击**：单指轻触，等同于鼠标左键
- **长按**：单指长时间按压，显示上下文菜单
- **滑动**：单指滑动，用于滚动和翻页
- **缩放**：双指捏合/展开，用于缩放内容

**触控优化设计：**
```css
/* 触控友好的按钮尺寸 */
.touch-target {
    min-width: 44px;
    min-height: 44px;
    padding: 8px 16px;
}

/* 触控反馈 */
.touch-feedback {
    position: relative;
    overflow: hidden;
}

.touch-feedback::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.touch-feedback:active::after {
    width: 200px;
    height: 200px;
}
```

## 3. 组件交互规范

### 3.1 按钮交互设计
**按钮状态交互：**
```css
/* 按钮基础状态 */
.button {
    position: relative;
    overflow: hidden;
    transition: all 150ms cubic-bezier(0.4, 0.0, 0.2, 1);
    transform: translateZ(0); /* 启用硬件加速 */
}

/* 悬停状态 */
.button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* 按下状态 */
.button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* 焦点状态 */
.button:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

/* 加载状态 */
.button.loading {
    pointer-events: none;
    position: relative;
}

.button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: button-loading 1s linear infinite;
}

@keyframes button-loading {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 成功状态 */
.button.success {
    background: var(--color-success);
    animation: button-success 0.3s ease-out;
}

@keyframes button-success {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
```

**按钮组交互：**
```css
/* 按钮组 */
.button-group {
    display: flex;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 2px rgba(0,0,0,0.133);
}

.button-group .button {
    border-radius: 0;
    border-right: 1px solid var(--color-border);
    margin: 0;
}

.button-group .button:first-child {
    border-radius: 4px 0 0 4px;
}

.button-group .button:last-child {
    border-radius: 0 4px 4px 0;
    border-right: none;
}

.button-group .button.active {
    background: var(--color-primary);
    color: #FFFFFF;
    z-index: 1;
}
```

### 3.2 表单交互设计
**输入框交互：**
```css
/* 输入框状态 */
.input-field {
    position: relative;
    margin-bottom: 16px;
}

.input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--color-border);
    border-radius: 4px;
    font: 14px/20px Segoe UI;
    transition: all 150ms cubic-bezier(0.4, 0.0, 0.2, 1);
    background: var(--color-background);
}

/* 焦点状态 */
.input:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 1px var(--color-primary);
    outline: none;
}

/* 错误状态 */
.input.error {
    border-color: var(--color-error);
    box-shadow: 0 0 0 1px var(--color-error);
}

/* 成功状态 */
.input.success {
    border-color: var(--color-success);
    box-shadow: 0 0 0 1px var(--color-success);
}

/* 浮动标签效果 */
.input-field.floating-label .input-label {
    position: absolute;
    top: 12px;
    left: 16px;
    color: var(--color-text-tertiary);
    transition: all 150ms cubic-bezier(0.4, 0.0, 0.2, 1);
    pointer-events: none;
    background: var(--color-background);
    padding: 0 4px;
}

.input-field.floating-label .input:focus + .input-label,
.input-field.floating-label .input:not(:placeholder-shown) + .input-label {
    top: -8px;
    left: 12px;
    font-size: 12px;
    color: var(--color-primary);
}
```

**表单验证交互：**
```javascript
// 实时表单验证
class FormValidator {
    constructor(form) {
        this.form = form;
        this.rules = {};
        this.errors = {};
        this.init();
    }
    
    init() {
        // 绑定输入事件
        this.form.addEventListener('input', this.handleInput.bind(this));
        this.form.addEventListener('blur', this.handleBlur.bind(this), true);
        this.form.addEventListener('submit', this.handleSubmit.bind(this));
    }
    
    handleInput(event) {
        const field = event.target;
        if (field.matches('.validate-on-input')) {
            this.validateField(field);
        }
    }
    
    handleBlur(event) {
        const field = event.target;
        if (field.matches('.validate-on-blur')) {
            this.validateField(field);
        }
    }
    
    validateField(field) {
        const value = field.value;
        const rules = this.rules[field.name];
        const errors = [];
        
        // 执行验证规则
        if (rules) {
            rules.forEach(rule => {
                if (!rule.test(value)) {
                    errors.push(rule.message);
                }
            });
        }
        
        // 更新UI状态
        this.updateFieldState(field, errors);
        
        return errors.length === 0;
    }
    
    updateFieldState(field, errors) {
        const fieldContainer = field.closest('.input-field');
        const errorContainer = fieldContainer.querySelector('.field-error');
        
        // 移除之前的状态
        field.classList.remove('error', 'success');
        
        if (errors.length > 0) {
            // 显示错误状态
            field.classList.add('error');
            if (errorContainer) {
                errorContainer.textContent = errors[0];
                errorContainer.style.display = 'block';
            }
        } else if (field.value) {
            // 显示成功状态
            field.classList.add('success');
            if (errorContainer) {
                errorContainer.style.display = 'none';
            }
        }
    }
}
```

### 3.3 导航交互设计
**主导航交互：**
```css
/* 导航项交互 */
.navigation-item {
    position: relative;
    transition: all 150ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

/* 悬停效果 */
.navigation-item:hover {
    background: var(--color-surface);
    transform: translateX(2px);
}

/* 选中状态 */
.navigation-item.selected {
    background: var(--color-primary);
    color: #FFFFFF;
}

.navigation-item.selected::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: #FFFFFF;
}

/* 展开/折叠动画 */
.navigation-submenu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 250ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

.navigation-item.expanded .navigation-submenu {
    max-height: 500px; /* 足够大的值 */
}

/* 导航折叠按钮 */
.navigation-toggle {
    transition: transform 150ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

.navigation-item.expanded .navigation-toggle {
    transform: rotate(90deg);
}
```

**面包屑交互：**
```css
/* 面包屑悬停效果 */
.breadcrumb-item {
    position: relative;
    transition: all 150ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

.breadcrumb-item:hover {
    color: var(--color-primary);
    transform: translateY(-1px);
}

.breadcrumb-item:hover::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--color-primary);
    animation: breadcrumb-underline 150ms ease-out;
}

@keyframes breadcrumb-underline {
    from { transform: scaleX(0); }
    to { transform: scaleX(1); }
}
```

## 4. 数据交互规范

### 4.1 表格交互设计
**表格行选择：**
```css
/* 表格行交互 */
.table-row {
    transition: all 150ms cubic-bezier(0.4, 0.0, 0.2, 1);
    cursor: pointer;
}

.table-row:hover {
    background: var(--color-surface);
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.table-row.selected {
    background: rgba(0, 120, 212, 0.1);
    border-left: 3px solid var(--color-primary);
}

.table-row.selected:hover {
    background: rgba(0, 120, 212, 0.15);
}

/* 多选模式 */
.table.multi-select .table-row {
    position: relative;
}

.table.multi-select .table-row::before {
    content: '';
    position: absolute;
    left: 8px;
    top: 50%;
    width: 16px;
    height: 16px;
    border: 1px solid var(--color-border);
    border-radius: 2px;
    background: var(--color-background);
    transform: translateY(-50%);
    opacity: 0;
    transition: opacity 150ms;
}

.table.multi-select .table-row:hover::before,
.table.multi-select .table-row.selected::before {
    opacity: 1;
}

.table.multi-select .table-row.selected::before {
    background: var(--color-primary);
    border-color: var(--color-primary);
}

.table.multi-select .table-row.selected::after {
    content: '✓';
    position: absolute;
    left: 10px;
    top: 50%;
    color: #FFFFFF;
    font-size: 12px;
    transform: translateY(-50%);
}
```

**表格排序交互：**
```css
/* 可排序列头 */
.table-header.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
    transition: background 150ms;
}

.table-header.sortable:hover {
    background: var(--color-surface);
}

/* 排序指示器 */
.table-header.sortable::after {
    content: '';
    position: absolute;
    right: 8px;
    top: 50%;
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid var(--color-text-tertiary);
    transform: translateY(-50%);
    opacity: 0.3;
    transition: all 150ms;
}

.table-header.sortable.sort-asc::after {
    border-bottom: 4px solid var(--color-primary);
    border-top: none;
    opacity: 1;
}

.table-header.sortable.sort-desc::after {
    border-top: 4px solid var(--color-primary);
    border-bottom: none;
    opacity: 1;
}
```

### 4.2 图表交互设计
**图表悬停交互：**
```javascript
// 图表交互管理
class ChartInteraction {
    constructor(chart) {
        this.chart = chart;
        this.tooltip = null;
        this.init();
    }
    
    init() {
        this.createTooltip();
        this.bindEvents();
    }
    
    createTooltip() {
        this.tooltip = document.createElement('div');
        this.tooltip.className = 'chart-tooltip';
        this.tooltip.style.cssText = `
            position: absolute;
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: 4px;
            padding: 8px 12px;
            font-size: 12px;
            box-shadow: var(--shadow-elevation-4);
            pointer-events: none;
            opacity: 0;
            transition: opacity 150ms;
            z-index: 1000;
        `;
        document.body.appendChild(this.tooltip);
    }
    
    bindEvents() {
        this.chart.addEventListener('mousemove', this.handleMouseMove.bind(this));
        this.chart.addEventListener('mouseleave', this.handleMouseLeave.bind(this));
        this.chart.addEventListener('click', this.handleClick.bind(this));
    }
    
    handleMouseMove(event) {
        const dataPoint = this.getDataPointAtPosition(event.clientX, event.clientY);
        
        if (dataPoint) {
            this.showTooltip(dataPoint, event.clientX, event.clientY);
            this.highlightDataPoint(dataPoint);
        } else {
            this.hideTooltip();
            this.clearHighlight();
        }
    }
    
    showTooltip(dataPoint, x, y) {
        this.tooltip.innerHTML = `
            <div class="tooltip-title">${dataPoint.label}</div>
            <div class="tooltip-value">${dataPoint.value}</div>
            <div class="tooltip-time">${dataPoint.timestamp}</div>
        `;
        
        this.tooltip.style.left = `${x + 10}px`;
        this.tooltip.style.top = `${y - 10}px`;
        this.tooltip.style.opacity = '1';
    }
    
    hideTooltip() {
        this.tooltip.style.opacity = '0';
    }
}
```

## 5. 状态反馈交互

### 5.1 加载状态交互
**全局加载状态：**
```css
/* 全局加载遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 16px;
    z-index: 9999;
    opacity: 0;
    pointer-events: none;
    transition: opacity 250ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

.loading-overlay.active {
    opacity: 1;
    pointer-events: all;
}

/* 加载动画 */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--color-surface-tertiary);
    border-top: 3px solid var(--color-primary);
    border-radius: 50%;
    animation: loading-spin 1s linear infinite;
}

@keyframes loading-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 加载文本 */
.loading-text {
    font: 14px/20px Segoe UI;
    color: var(--color-text-secondary);
    text-align: center;
}

/* 加载进度条 */
.loading-progress {
    width: 200px;
    height: 4px;
    background: var(--color-surface-tertiary);
    border-radius: 2px;
    overflow: hidden;
}

.loading-progress-fill {
    height: 100%;
    background: var(--color-primary);
    border-radius: 2px;
    transition: width 250ms cubic-bezier(0.4, 0.0, 0.2, 1);
}
```

**局部加载状态：**
```css
/* 组件加载状态 */
.component-loading {
    position: relative;
    pointer-events: none;
}

.component-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.component-loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--color-surface-tertiary);
    border-top: 2px solid var(--color-primary);
    border-radius: 50%;
    animation: loading-spin 1s linear infinite;
    z-index: 11;
}
```

### 5.2 错误状态交互
**错误提示交互：**
```css
/* 错误消息 */
.error-message {
    background: var(--color-error);
    color: #FFFFFF;
    padding: 12px 16px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    animation: error-slide-in 250ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

@keyframes error-slide-in {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.error-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
}

.error-content {
    flex: 1;
}

.error-title {
    font-weight: 600;
    margin-bottom: 4px;
}

.error-description {
    font-size: 12px;
    opacity: 0.9;
}

.error-close {
    width: 16px;
    height: 16px;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 150ms;
}

.error-close:hover {
    opacity: 1;
}

/* 错误恢复按钮 */
.error-actions {
    margin-top: 8px;
    display: flex;
    gap: 8px;
}

.error-action {
    background: rgba(255, 255, 255, 0.2);
    color: #FFFFFF;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 4px 8px;
    border-radius: 2px;
    font-size: 12px;
    cursor: pointer;
    transition: background 150ms;
}

.error-action:hover {
    background: rgba(255, 255, 255, 0.3);
}
```
