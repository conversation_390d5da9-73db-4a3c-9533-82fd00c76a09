# AirMonitor 动效设计规范

## 1. 动效设计原则

### 1.1 Fluent Motion 设计原则
**自然性 (Natural)：**
- 模拟真实世界的物理运动规律
- 使用重力、摩擦力、弹性等物理概念
- 动效应该感觉自然而不突兀
- 遵循用户对物体运动的直觉认知

**功能性 (Functional)：**
- 动效必须有明确的功能目的
- 帮助用户理解界面变化和状态转换
- 引导用户注意力到重要信息
- 提供操作反馈和状态确认

**高效性 (Efficient)：**
- 动效时长控制在合理范围内
- 不干扰用户的主要任务流程
- 支持用户跳过或加速动画
- 在低性能设备上优雅降级

**一致性 (Consistent)：**
- 相同类型的操作使用相同的动效
- 动效参数在整个应用中保持统一
- 遵循平台的动效规范和习惯
- 建立用户可预期的动效模式

### 1.2 专业工具软件动效特色
**稳重可靠：**
- 避免过于花哨或娱乐性的动效
- 动效应该传达专业性和可靠性
- 重要操作的动效应该给用户信心
- 错误状态的动效应该清晰明确

**效率导向：**
- 动效不应该拖慢用户的工作效率
- 提供快速完成动画的选项
- 重复操作的动效可以逐渐简化
- 支持用户自定义动效偏好

## 2. 动效时长和缓动

### 2.1 标准动效时长
```css
/* 动效时长变量定义 */
:root {
    /* 微交互动效 */
    --duration-instant: 100ms;      /* 即时反馈 */
    --duration-quick: 150ms;        /* 快速交互 */
    --duration-short: 250ms;        /* 短动效 */
    
    /* 标准动效 */
    --duration-medium: 300ms;       /* 标准过渡 */
    --duration-long: 400ms;         /* 长动效 */
    --duration-extended: 500ms;     /* 扩展动效 */
    
    /* 特殊动效 */
    --duration-slow: 800ms;         /* 慢动效 */
    --duration-very-slow: 1200ms;   /* 很慢的动效 */
}
```

**动效时长使用指南：**
- **100ms (Instant)**：按钮悬停、焦点变化、颜色过渡
- **150ms (Quick)**：小元素的显示隐藏、工具提示
- **250ms (Short)**：菜单展开、下拉框、小面板
- **300ms (Medium)**：页面切换、模态窗口、卡片翻转
- **400ms (Long)**：大面板滑动、复杂状态变化
- **500ms (Extended)**：页面级动画、数据加载动画

### 2.2 缓动函数规范
```css
/* 缓动函数变量定义 */
:root {
    /* 标准缓动 */
    --ease-standard: cubic-bezier(0.4, 0.0, 0.2, 1);
    --ease-decelerate: cubic-bezier(0.0, 0.0, 0.2, 1);
    --ease-accelerate: cubic-bezier(0.4, 0.0, 1, 1);
    
    /* 特殊缓动 */
    --ease-bounce: cubic-bezier(0.175, 0.885, 0.32, 1.275);
    --ease-elastic: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --ease-back: cubic-bezier(0.68, -0.6, 0.32, 1.6);
    
    /* 物理缓动 */
    --ease-spring: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --ease-smooth: cubic-bezier(0.25, 0.1, 0.25, 1);
}
```

**缓动函数使用场景：**
- **Standard**：通用过渡动画，页面切换
- **Decelerate**：元素进入动画，显示动画
- **Accelerate**：元素退出动画，隐藏动画
- **Bounce**：确认操作，成功状态
- **Elastic**：错误提示，注意力引导
- **Spring**：自然的物理运动

## 3. 基础动效类型

### 3.1 淡入淡出动效
```css
/* 淡入动效 */
@keyframes fade-in {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* 淡出动效 */
@keyframes fade-out {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* 淡入淡出类 */
.fade-in {
    animation: fade-in var(--duration-short) var(--ease-decelerate);
}

.fade-out {
    animation: fade-out var(--duration-short) var(--ease-accelerate);
}

/* 渐进淡入 */
@keyframes fade-in-up {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fade-in-up var(--duration-medium) var(--ease-decelerate);
}

/* 渐进淡出 */
@keyframes fade-out-down {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(20px);
    }
}

.fade-out-down {
    animation: fade-out-down var(--duration-medium) var(--ease-accelerate);
}
```

### 3.2 滑动动效
```css
/* 从右侧滑入 */
@keyframes slide-in-right {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

/* 向右侧滑出 */
@keyframes slide-out-right {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(100%);
    }
}

/* 从左侧滑入 */
@keyframes slide-in-left {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* 向左侧滑出 */
@keyframes slide-out-left {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(-100%);
    }
}

/* 滑动类 */
.slide-in-right {
    animation: slide-in-right var(--duration-medium) var(--ease-decelerate);
}

.slide-out-right {
    animation: slide-out-right var(--duration-medium) var(--ease-accelerate);
}

.slide-in-left {
    animation: slide-in-left var(--duration-medium) var(--ease-decelerate);
}

.slide-out-left {
    animation: slide-out-left var(--duration-medium) var(--ease-accelerate);
}
```

### 3.3 缩放动效
```css
/* 缩放进入 */
@keyframes scale-in {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 缩放退出 */
@keyframes scale-out {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.8);
    }
}

/* 弹性缩放进入 */
@keyframes scale-in-bounce {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* 缩放类 */
.scale-in {
    animation: scale-in var(--duration-short) var(--ease-decelerate);
}

.scale-out {
    animation: scale-out var(--duration-short) var(--ease-accelerate);
}

.scale-in-bounce {
    animation: scale-in-bounce var(--duration-long) var(--ease-bounce);
}
```

### 3.4 旋转动效
```css
/* 旋转进入 */
@keyframes rotate-in {
    from {
        opacity: 0;
        transform: rotate(-180deg) scale(0.8);
    }
    to {
        opacity: 1;
        transform: rotate(0deg) scale(1);
    }
}

/* 连续旋转 */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 摆动 */
@keyframes wiggle {
    0%, 7% {
        transform: rotateZ(0);
    }
    15% {
        transform: rotateZ(-15deg);
    }
    20% {
        transform: rotateZ(10deg);
    }
    25% {
        transform: rotateZ(-10deg);
    }
    30% {
        transform: rotateZ(6deg);
    }
    35% {
        transform: rotateZ(-4deg);
    }
    40%, 100% {
        transform: rotateZ(0);
    }
}

/* 旋转类 */
.rotate-in {
    animation: rotate-in var(--duration-medium) var(--ease-decelerate);
}

.spin {
    animation: spin 1s linear infinite;
}

.wiggle {
    animation: wiggle 0.8s var(--ease-standard);
}
```

## 4. 状态动效

### 4.1 加载动效
```css
/* 脉冲加载 */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.05);
    }
}

/* 呼吸加载 */
@keyframes breathe {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* 波纹加载 */
@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

/* 骨架屏加载 */
@keyframes skeleton-loading {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* 加载类 */
.pulse {
    animation: pulse 2s var(--ease-standard) infinite;
}

.breathe {
    animation: breathe 3s var(--ease-standard) infinite;
}

.ripple {
    animation: ripple 1.5s var(--ease-standard) infinite;
}

.skeleton-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: skeleton-loading 1.5s infinite;
}
```

### 4.2 成功状态动效
```css
/* 成功检查标记 */
@keyframes success-check {
    0% {
        stroke-dashoffset: 100;
    }
    100% {
        stroke-dashoffset: 0;
    }
}

/* 成功缩放 */
@keyframes success-scale {
    0% {
        transform: scale(0);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

/* 成功光晕 */
@keyframes success-glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(16, 124, 16, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(16, 124, 16, 0.8);
    }
}

/* 成功类 */
.success-check {
    animation: success-check var(--duration-medium) var(--ease-decelerate);
}

.success-scale {
    animation: success-scale var(--duration-long) var(--ease-bounce);
}

.success-glow {
    animation: success-glow 2s var(--ease-standard) infinite;
}
```

### 4.3 错误状态动效
```css
/* 错误摇摆 */
@keyframes error-shake {
    0%, 20%, 40%, 60%, 80% {
        transform: translateX(-10px);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(10px);
    }
    100% {
        transform: translateX(0);
    }
}

/* 错误闪烁 */
@keyframes error-blink {
    0%, 50% {
        opacity: 1;
    }
    25%, 75% {
        opacity: 0.5;
    }
}

/* 错误边框 */
@keyframes error-border {
    0%, 100% {
        border-color: var(--color-error);
    }
    50% {
        border-color: transparent;
    }
}

/* 错误类 */
.error-shake {
    animation: error-shake var(--duration-long) var(--ease-standard);
}

.error-blink {
    animation: error-blink 1s var(--ease-standard) infinite;
}

.error-border {
    animation: error-border 1s var(--ease-standard) infinite;
}
```

## 5. 交互动效

### 5.1 悬停动效
```css
/* 悬停上浮 */
.hover-lift {
    transition: transform var(--duration-quick) var(--ease-standard),
                box-shadow var(--duration-quick) var(--ease-standard);
}

.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 悬停缩放 */
.hover-scale {
    transition: transform var(--duration-quick) var(--ease-standard);
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* 悬停发光 */
.hover-glow {
    transition: box-shadow var(--duration-quick) var(--ease-standard);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(0, 120, 212, 0.3);
}

/* 悬停倾斜 */
.hover-tilt {
    transition: transform var(--duration-quick) var(--ease-standard);
}

.hover-tilt:hover {
    transform: perspective(1000px) rotateX(10deg) rotateY(10deg);
}
```

### 5.2 点击动效
```css
/* 点击波纹 */
@keyframes click-ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

.click-ripple {
    position: relative;
    overflow: hidden;
}

.click-ripple::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    transform: translate(-50%, -50%);
    animation: click-ripple 0.6s var(--ease-standard);
}

/* 点击缩放 */
.click-scale {
    transition: transform var(--duration-instant) var(--ease-standard);
}

.click-scale:active {
    transform: scale(0.95);
}

/* 点击下沉 */
.click-press {
    transition: transform var(--duration-instant) var(--ease-standard),
                box-shadow var(--duration-instant) var(--ease-standard);
}

.click-press:active {
    transform: translateY(1px);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
```

### 5.3 焦点动效
```css
/* 焦点环 */
.focus-ring {
    transition: box-shadow var(--duration-quick) var(--ease-standard);
}

.focus-ring:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--color-primary);
}

/* 焦点发光 */
.focus-glow {
    transition: box-shadow var(--duration-quick) var(--ease-standard);
}

.focus-glow:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--color-primary),
                0 0 10px rgba(0, 120, 212, 0.3);
}

/* 焦点缩放 */
.focus-scale {
    transition: transform var(--duration-quick) var(--ease-standard);
}

.focus-scale:focus {
    transform: scale(1.02);
}
```

## 6. 页面转换动效

### 6.1 页面切换动效
```css
/* 页面淡入切换 */
.page-transition-fade {
    opacity: 0;
    transition: opacity var(--duration-medium) var(--ease-standard);
}

.page-transition-fade.active {
    opacity: 1;
}

/* 页面滑动切换 */
.page-transition-slide {
    transform: translateX(100%);
    transition: transform var(--duration-medium) var(--ease-decelerate);
}

.page-transition-slide.active {
    transform: translateX(0);
}

.page-transition-slide.exit {
    transform: translateX(-100%);
    transition: transform var(--duration-medium) var(--ease-accelerate);
}

/* 页面缩放切换 */
.page-transition-scale {
    opacity: 0;
    transform: scale(0.9);
    transition: opacity var(--duration-medium) var(--ease-decelerate),
                transform var(--duration-medium) var(--ease-decelerate);
}

.page-transition-scale.active {
    opacity: 1;
    transform: scale(1);
}
```

### 6.2 模态窗口动效
```css
/* 模态窗口遮罩 */
.modal-overlay {
    opacity: 0;
    transition: opacity var(--duration-medium) var(--ease-standard);
}

.modal-overlay.active {
    opacity: 1;
}

/* 模态窗口内容 */
.modal-content {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
    transition: opacity var(--duration-medium) var(--ease-decelerate),
                transform var(--duration-medium) var(--ease-decelerate);
}

.modal-content.active {
    opacity: 1;
    transform: scale(1) translateY(0);
}

/* 模态窗口从底部滑入 */
.modal-slide-up {
    transform: translateY(100%);
    transition: transform var(--duration-medium) var(--ease-decelerate);
}

.modal-slide-up.active {
    transform: translateY(0);
}
```

## 7. 数据动效

### 7.1 数据更新动效
```css
/* 数据闪烁更新 */
@keyframes data-flash {
    0%, 100% {
        background-color: transparent;
    }
    50% {
        background-color: rgba(0, 120, 212, 0.1);
    }
}

.data-update {
    animation: data-flash var(--duration-medium) var(--ease-standard);
}

/* 数据计数动效 */
@keyframes count-up {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.count-animation {
    animation: count-up var(--duration-short) var(--ease-decelerate);
}

/* 进度条动效 */
.progress-animated {
    transition: width var(--duration-long) var(--ease-decelerate);
}
```

### 7.2 图表动效
```css
/* 图表线条绘制 */
@keyframes draw-line {
    from {
        stroke-dashoffset: 1000;
    }
    to {
        stroke-dashoffset: 0;
    }
}

.chart-line-animated {
    stroke-dasharray: 1000;
    animation: draw-line 2s var(--ease-decelerate);
}

/* 柱状图增长 */
@keyframes bar-grow {
    from {
        transform: scaleY(0);
        transform-origin: bottom;
    }
    to {
        transform: scaleY(1);
        transform-origin: bottom;
    }
}

.chart-bar-animated {
    animation: bar-grow var(--duration-long) var(--ease-decelerate);
}
```

## 8. 性能优化

### 8.1 硬件加速
```css
/* 启用硬件加速 */
.hardware-accelerated {
    transform: translateZ(0);
    will-change: transform;
}

/* 动画期间启用硬件加速 */
.animating {
    will-change: transform, opacity;
}

.animating.finished {
    will-change: auto;
}
```

### 8.2 性能监控
```javascript
// 动效性能监控
class AnimationPerformance {
    constructor() {
        this.frameCount = 0;
        this.startTime = 0;
        this.isMonitoring = false;
    }
    
    startMonitoring() {
        this.isMonitoring = true;
        this.frameCount = 0;
        this.startTime = performance.now();
        this.monitorFrame();
    }
    
    monitorFrame() {
        if (!this.isMonitoring) return;
        
        this.frameCount++;
        requestAnimationFrame(() => this.monitorFrame());
    }
    
    stopMonitoring() {
        this.isMonitoring = false;
        const duration = performance.now() - this.startTime;
        const fps = (this.frameCount / duration) * 1000;
        
        console.log(`Animation FPS: ${fps.toFixed(2)}`);
        
        if (fps < 30) {
            console.warn('Low animation performance detected');
        }
    }
}
```

### 8.3 降级策略
```css
/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 低性能设备降级 */
.low-performance .complex-animation {
    animation: none;
    transition: none;
}

.low-performance .simple-fade {
    transition: opacity var(--duration-quick) var(--ease-standard);
}
```
