# AirMonitor 原型制作规范

## 1. 原型制作总体规划

### 1.1 原型类型定义
**低保真原型（线框图）：**
- **目的**：验证信息架构和基本布局
- **内容**：页面结构、内容布局、导航流程
- **工具**：Figma Wireframe Kit
- **细节程度**：框架结构，无视觉细节
- **交付时间**：1-2天

**中保真原型：**
- **目的**：验证交互流程和功能逻辑
- **内容**：基本视觉样式、交互状态、内容结构
- **工具**：Figma + Fluent UI Kit
- **细节程度**：基础样式，简化视觉
- **交付时间**：3-4天

**高保真原型：**
- **目的**：最终设计效果展示和开发交付
- **内容**：完整视觉设计、真实内容、完整交互
- **工具**：Figma + Interactive Components
- **细节程度**：像素级精确，接近最终产品
- **交付时间**：5-7天

### 1.2 原型制作优先级
**P0级原型（核心流程）：**
1. License激活流程原型
2. 设备连接流程原型
3. 实时监控主界面原型
4. 基础设备控制原型

**P1级原型（重要功能）：**
1. 数据分析界面原型
2. 参数管理界面原型
3. 报警管理界面原型
4. 系统设置界面原型

**P2级原型（扩展功能）：**
1. 故障诊断界面原型
2. 测试工具界面原型
3. 协作功能原型
4. 报告生成原型

### 1.3 原型文件组织结构
```
05_原型图/
├── 01_线框图/
│   ├── 主界面线框图.fig
│   ├── License激活线框图.fig
│   ├── 设备连接线框图.fig
│   └── 监控界面线框图.fig
├── 02_中保真原型/
│   ├── 核心流程原型.fig
│   ├── 功能模块原型.fig
│   └── 交互状态原型.fig
├── 03_高保真原型/
│   ├── AirMonitor_完整原型.fig
│   ├── 明亮主题原型.fig
│   ├── 暗色主题原型.fig
│   └── 高对比度原型.fig
├── 04_交互演示/
│   ├── 用户流程演示.mp4
│   ├── 功能操作演示.mp4
│   └── 主题切换演示.mp4
└── 05_原型文档/
    ├── 原型使用说明.md
    ├── 交互规范文档.md
    └── 开发标注文档.md
```

## 2. Figma原型制作规范

### 2.1 文件命名规范
**文件命名格式：**
```
AirMonitor_[模块名称]_[保真度]_v[版本号]
```

**示例：**
- `AirMonitor_License激活_高保真_v1.0.fig`
- `AirMonitor_实时监控_中保真_v2.1.fig`
- `AirMonitor_完整原型_高保真_v3.0.fig`

### 2.2 页面命名规范
**页面命名格式：**
```
[模块编号]_[页面名称]_[状态]
```

**示例：**
- `01_License激活_默认状态`
- `02_设备连接_连接中状态`
- `03_实时监控_报警状态`
- `04_参数管理_编辑状态`

### 2.3 组件命名规范
**组件命名格式：**
```
[类型]/[组件名称]/[变体]
```

**示例：**
- `Button/Primary/Default`
- `Button/Primary/Hover`
- `Card/Monitor/Normal`
- `Card/Monitor/Alert`

### 2.4 图层组织规范
```
页面结构
├── 🎨 Design System
│   ├── Colors
│   ├── Typography
│   ├── Icons
│   └── Components
├── 📱 Screens
│   ├── 01_License Activation
│   ├── 02_Device Connection
│   ├── 03_Real-time Monitoring
│   ├── 04_Device Control
│   ├── 05_Data Analysis
│   └── 06_System Settings
├── 🔄 Flows
│   ├── User Onboarding Flow
│   ├── Connection Setup Flow
│   ├── Monitoring Workflow
│   └── Error Handling Flow
└── 📋 Documentation
    ├── Interaction Notes
    ├── Development Specs
    └── Version History
```

## 3. 交互原型规范

### 3.1 交互触发器定义
**点击交互：**
- **On Click**：页面跳转、状态切换、操作执行
- **On Hover**：悬停效果、工具提示、预览
- **On Press**：按下效果、触觉反馈

**键盘交互：**
- **On Key Down**：快捷键操作、导航控制
- **Key: Tab**：焦点导航
- **Key: Enter**：确认操作
- **Key: Escape**：取消操作

**时间触发：**
- **After Delay**：自动播放、超时处理
- **Timer**：倒计时、进度更新

### 3.2 动画过渡规范
**过渡类型：**
- **Instant**：即时切换（0ms）
- **Quick**：快速过渡（150ms）
- **Standard**：标准过渡（250ms）
- **Slow**：慢速过渡（400ms）

**缓动函数：**
- **Ease Out**：元素进入动画
- **Ease In**：元素退出动画
- **Ease In Out**：状态变化动画
- **Spring**：弹性动画效果

### 3.3 状态管理规范
**组件状态：**
- **Default**：默认状态
- **Hover**：悬停状态
- **Active**：激活状态
- **Disabled**：禁用状态
- **Loading**：加载状态
- **Error**：错误状态
- **Success**：成功状态

**页面状态：**
- **Empty**：空状态
- **Loading**：加载状态
- **Error**：错误状态
- **Offline**：离线状态

## 4. 核心原型制作计划

### 4.1 License激活流程原型
**原型页面清单：**
1. **激活欢迎页**
   - 软件Logo和品牌信息
   - 激活流程说明
   - 开始激活按钮

2. **License文件导入页**
   - 文件拖拽上传区域
   - 文件选择按钮
   - 上传进度指示
   - 文件验证状态

3. **设备指纹生成页**
   - 硬件信息收集进度
   - 设备指纹显示
   - 指纹复制功能
   - 下一步操作

4. **License验证页**
   - 验证进度动画
   - 验证步骤指示
   - 验证结果显示
   - 错误处理界面

5. **激活完成页**
   - 成功确认信息
   - 用户权限显示
   - 进入软件按钮
   - 激活信息摘要

**交互流程设计：**
```
激活欢迎页 → 文件导入页 → 指纹生成页 → 验证页 → 完成页
     ↓           ↓           ↓         ↓        ↓
   开始激活    文件上传    指纹生成   License验证  进入软件
     ↓           ↓           ↓         ↓        ↓
   流程说明    进度反馈    复制指纹   状态动画   权限确认
```

### 4.2 设备连接流程原型
**原型页面清单：**
1. **连接管理主页**
   - 连接状态概览
   - 连接历史列表
   - 新建连接按钮
   - 快速连接选项

2. **新建连接向导**
   - 连接类型选择
   - 串口参数配置
   - 连接测试界面
   - 连接保存确认

3. **连接状态监控**
   - 实时连接状态
   - 连接质量指示
   - 数据传输统计
   - 连接操作控制

4. **连接故障处理**
   - 故障诊断界面
   - 解决方案建议
   - 重新连接操作
   - 技术支持联系

**交互流程设计：**
```
连接管理 → 新建向导 → 参数配置 → 连接测试 → 状态监控
    ↓         ↓         ↓         ↓         ↓
  历史连接   类型选择   串口设置   测试结果   质量监控
    ↓         ↓         ↓         ↓         ↓
  快速连接   向导步骤   参数验证   错误处理   操作控制
```

### 4.3 实时监控界面原型
**原型页面清单：**
1. **监控仪表板**
   - 设备状态卡片
   - 实时数据显示
   - 报警信息面板
   - 快捷操作区域

2. **数据可视化**
   - 实时曲线图表
   - 历史数据对比
   - 参数趋势分析
   - 数据导出功能

3. **报警管理**
   - 报警列表显示
   - 报警详情查看
   - 报警处理操作
   - 报警设置配置

4. **监控配置**
   - 显示参数设置
   - 刷新频率配置
   - 报警阈值设置
   - 界面布局调整

**交互流程设计：**
```
仪表板 → 数据可视化 → 报警管理 → 监控配置
   ↓         ↓           ↓         ↓
 状态卡片   实时图表     报警列表   参数设置
   ↓         ↓           ↓         ↓
 数据更新   图表交互     处理操作   配置保存
```

## 5. 原型交互设计

### 5.1 导航交互原型
**主导航交互：**
```javascript
// Figma交互设置示例
Navigation_Item_Click: {
    trigger: "On Click",
    action: "Navigate to",
    destination: "Target_Page",
    animation: "Smart Animate",
    duration: "250ms",
    easing: "Ease Out"
}

Navigation_Hover: {
    trigger: "On Hover",
    action: "Change to",
    destination: "Hover_State",
    animation: "Instant",
    duration: "0ms"
}
```

**面包屑导航：**
```javascript
Breadcrumb_Click: {
    trigger: "On Click",
    action: "Navigate to",
    destination: "Parent_Page",
    animation: "Move In",
    direction: "Left",
    duration: "300ms",
    easing: "Ease In Out"
}
```

### 5.2 表单交互原型
**输入框交互：**
```javascript
Input_Focus: {
    trigger: "On Click",
    action: "Change to",
    destination: "Focus_State",
    animation: "Smart Animate",
    duration: "150ms"
}

Input_Validation: {
    trigger: "While Typing",
    action: "Change to",
    destination: "Error_State",
    condition: "Invalid Input",
    animation: "Dissolve",
    duration: "200ms"
}
```

**按钮交互：**
```javascript
Button_Hover: {
    trigger: "On Hover",
    action: "Change to",
    destination: "Hover_State",
    animation: "Smart Animate",
    duration: "150ms"
}

Button_Click: {
    trigger: "On Click",
    action: "Change to",
    destination: "Loading_State",
    animation: "Smart Animate",
    duration: "100ms",
    then: {
        delay: "2000ms",
        action: "Change to",
        destination: "Success_State"
    }
}
```

### 5.3 数据交互原型
**图表交互：**
```javascript
Chart_Hover: {
    trigger: "On Hover",
    action: "Show Overlay",
    overlay: "Tooltip_Component",
    position: "Mouse Position",
    animation: "Fade In",
    duration: "150ms"
}

Chart_Click: {
    trigger: "On Click",
    action: "Navigate to",
    destination: "Detail_View",
    animation: "Push",
    direction: "Left",
    duration: "300ms"
}
```

**表格交互：**
```javascript
Table_Row_Hover: {
    trigger: "On Hover",
    action: "Change to",
    destination: "Hover_State",
    animation: "Smart Animate",
    duration: "100ms"
}

Table_Row_Select: {
    trigger: "On Click",
    action: "Change to",
    destination: "Selected_State",
    animation: "Smart Animate",
    duration: "150ms"
}
```

## 6. 原型测试规范

### 6.1 可用性测试计划
**测试目标：**
- 验证用户能否顺利完成核心任务
- 识别界面设计中的可用性问题
- 评估用户对交互流程的理解程度
- 收集用户对界面设计的反馈意见

**测试任务设计：**
1. **License激活任务**
   - 任务：完成软件的License激活流程
   - 成功标准：用户能在5分钟内完成激活
   - 观察要点：文件上传、指纹理解、验证等待

2. **设备连接任务**
   - 任务：建立与空调设备的连接
   - 成功标准：用户能正确配置连接参数
   - 观察要点：参数理解、测试流程、错误处理

3. **监控操作任务**
   - 任务：查看设备状态并处理报警
   - 成功标准：用户能快速定位和处理问题
   - 观察要点：信息查找、操作执行、状态理解

### 6.2 专家评审计划
**启发式评估：**
- **可见性**：系统状态是否清晰可见
- **匹配性**：界面是否符合用户心智模型
- **控制性**：用户是否能控制操作流程
- **一致性**：界面元素是否保持一致
- **错误预防**：是否有效预防用户错误
- **识别性**：功能是否容易识别和理解
- **灵活性**：是否支持不同用户的使用习惯
- **美观性**：界面设计是否简洁美观

**认知走查：**
- **目标形成**：用户能否明确操作目标
- **行动规划**：用户能否制定操作计划
- **行动执行**：用户能否正确执行操作
- **结果感知**：用户能否理解操作结果

### 6.3 技术可行性评估
**开发实现难度：**
- **界面复杂度**：评估界面实现的技术难度
- **交互复杂度**：评估交互逻辑的实现难度
- **性能要求**：评估原型对系统性能的要求
- **兼容性要求**：评估跨平台兼容性需求

**资源需求评估：**
- **开发时间**：预估各模块的开发时间
- **技术栈**：确定所需的技术栈和工具
- **团队技能**：评估团队技能匹配度
- **外部依赖**：识别外部库和服务依赖

## 7. 原型交付规范

### 7.1 原型文件交付
**Figma文件结构：**
```
AirMonitor_完整原型_v1.0.fig
├── 📋 Cover & Index
├── 🎨 Design System
│   ├── Colors & Typography
│   ├── Icons & Illustrations
│   ├── Components Library
│   └── Interaction States
├── 📱 User Flows
│   ├── License Activation Flow
│   ├── Device Connection Flow
│   ├── Monitoring Workflow
│   └── Error Handling Flow
├── 🖥️ Desktop Screens
│   ├── Light Theme Screens
│   ├── Dark Theme Screens
│   └── High Contrast Screens
├── 🔄 Interactive Prototypes
│   ├── Main Prototype Flow
│   ├── Feature Demonstrations
│   └── Edge Case Scenarios
└── 📖 Documentation
    ├── Interaction Specifications
    ├── Development Notes
    └── Version History
```

### 7.2 原型演示文档
**演示脚本：**
1. **项目介绍**（2分钟）
   - 项目背景和目标
   - 用户群体和使用场景
   - 设计理念和原则

2. **核心流程演示**（10分钟）
   - License激活流程
   - 设备连接流程
   - 实时监控操作
   - 主要功能展示

3. **交互细节展示**（8分钟）
   - 动效和过渡效果
   - 状态变化和反馈
   - 错误处理和恢复
   - 响应式适配

4. **设计亮点说明**（5分钟）
   - 创新设计点
   - 用户体验优化
   - 技术实现考虑
   - 后续迭代计划

### 7.3 开发标注文档
**标注内容：**
- **尺寸标注**：元素尺寸、间距、边距
- **颜色标注**：色值、透明度、渐变
- **字体标注**：字体、字号、行高、字重
- **状态标注**：交互状态、动效参数
- **资源标注**：图标、图片、素材规格

**标注格式：**
```
元素名称: Button/Primary/Default
尺寸: 120px × 32px
颜色: Background #0078D4, Text #FFFFFF
字体: Segoe UI, 14px, Semibold
圆角: 4px
阴影: 0 2px 4px rgba(0,0,0,0.133)
交互: Hover → #106EBE, Active → #005A9E
动效: 150ms ease-out transition
```
