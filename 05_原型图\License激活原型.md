# AirMonitor License激活流程原型

## 1. 原型概述

### 1.1 原型目标
- **验证License激活流程的用户体验**
- **测试设备指纹生成和显示的可理解性**
- **确认激活过程中的状态反馈是否清晰**
- **评估错误处理流程的有效性**

### 1.2 原型范围
**包含页面：**
1. 激活欢迎页面
2. License文件导入页面
3. 设备指纹生成页面
4. License验证页面
5. 激活完成页面
6. 错误处理页面

**交互功能：**
- 完整的激活流程导航
- 文件拖拽上传交互
- 设备指纹生成动画
- 验证过程状态显示
- 错误状态处理和恢复

## 2. 页面原型设计

### 2.1 激活欢迎页面
**页面布局：**
```
┌─────────────────────────────────────────────────────────────┐
│                     AirMonitor 激活                         │
│                                                            │
│                    [🔐 Logo图标]                           │
│                                                            │
│                  欢迎使用 AirMonitor                        │
│              商用空调调试监控软件                            │
│                                                            │
│    请按照以下步骤完成软件激活，激活后即可使用全部功能          │
│                                                            │
│    ┌─────┐  ┌─────┐  ┌─────┐  ┌─────┐                    │
│    │  1  │→ │  2  │→ │  3  │→ │  4  │                    │
│    │导入 │  │生成 │  │验证 │  │完成 │                    │
│    │文件 │  │指纹 │  │激活 │  │设置 │                    │
│    └─────┘  └─────┘  └─────┘  └─────┘                    │
│                                                            │
│                  [开始激活] [稍后激活]                      │
│                                                            │
│              技术支持: <EMAIL>              │
└─────────────────────────────────────────────────────────────┘
```

**交互规范：**
```javascript
// 开始激活按钮
StartActivation_Click: {
    trigger: "On Click",
    action: "Navigate to",
    destination: "License_Import_Page",
    animation: "Slide Left",
    duration: "300ms",
    easing: "Ease Out"
}

// 稍后激活按钮
LaterActivation_Click: {
    trigger: "On Click",
    action: "Show Overlay",
    overlay: "Confirm_Dialog",
    animation: "Fade In",
    duration: "200ms"
}

// Logo动画
Logo_Animation: {
    trigger: "On Load",
    action: "Auto Animate",
    animation: "Pulse",
    duration: "2000ms",
    repeat: "Infinite"
}
```

### 2.2 License文件导入页面
**页面布局：**
```
┌─────────────────────────────────────────────────────────────┐
│  [←] 步骤 1/4: 导入License文件                              │
│                                                            │
│  ┌─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─┐  │
│  │                                                        │  │
│  │              [📁 上传图标]                              │  │
│  │                                                        │  │
│  │           拖拽License文件到此处                         │  │
│  │              或点击选择文件                             │  │
│  │                                                        │  │
│  │        支持格式: .lic, .license, .key                  │  │
│  │                                                        │  │
│  └ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─┘  │
│                                                            │
│                    [选择文件]                              │
│                                                            │
│  上传进度: ████████████████████████ 100%                   │
│                                                            │
│  ✅ License文件验证成功                                     │
│  📄 文件名: AirMonitor_License_2024.lic                    │
│  📊 文件大小: 2.3 KB                                       │
│  🏢 授权公司: 示例科技有限公司                              │
│                                                            │
│                              [上一步] [下一步]             │
└─────────────────────────────────────────────────────────────┘
```

**交互规范：**
```javascript
// 拖拽区域交互
DropZone_DragOver: {
    trigger: "On Drag Over",
    action: "Change to",
    destination: "DragOver_State",
    animation: "Smart Animate",
    duration: "150ms"
}

DropZone_Drop: {
    trigger: "On Drop",
    action: "Change to",
    destination: "Uploading_State",
    animation: "Smart Animate",
    duration: "200ms",
    then: {
        delay: "2000ms",
        action: "Change to",
        destination: "Success_State"
    }
}

// 文件选择按钮
FileSelect_Click: {
    trigger: "On Click",
    action: "Open File Dialog",
    fileTypes: [".lic", ".license", ".key"],
    then: {
        action: "Change to",
        destination: "Uploading_State"
    }
}

// 上传进度动画
UploadProgress_Animation: {
    trigger: "On State Change",
    property: "Width",
    from: "0%",
    to: "100%",
    duration: "2000ms",
    easing: "Ease Out"
}
```

### 2.3 设备指纹生成页面
**页面布局：**
```
┌─────────────────────────────────────────────────────────────┐
│  [←] 步骤 2/4: 生成设备指纹                                 │
│                                                            │
│  正在收集设备硬件信息...                                    │
│                                                            │
│  ✅ CPU信息收集完成     Intel Core i7-10700K               │
│  ✅ 主板信息收集完成    ASUS PRIME Z490-A                  │
│  🔄 硬盘信息收集中...   Samsung SSD 970 EVO                │
│  ⏳ 网卡信息等待中...   Realtek PCIe GbE                   │
│  ⏳ 内存信息等待中...   Corsair DDR4 32GB                  │
│                                                            │
│  ┌─────────────────────────────────────────────────────┐    │
│  │ 设备指纹:                                           │    │
│  │ A1B2-C3D4-E5F6-G7H8-I9J0-K1L2-M3N4-O5P6          │    │
│  │                                        [📋 复制]   │    │
│  └─────────────────────────────────────────────────────┘    │
│                                                            │
│  ⚠️ 重要提示:                                              │
│  • 设备指纹用于绑定License到当前设备                       │
│  • 更换主要硬件可能需要重新激活                            │
│  • 请妥善保管设备指纹信息                                  │
│                                                            │
│                              [上一步] [下一步]             │
└─────────────────────────────────────────────────────────────┘
```

**交互规范：**
```javascript
// 硬件信息收集动画
HardwareCollection_Animation: {
    trigger: "On Load",
    sequence: [
        {
            target: "CPU_Item",
            action: "Change to",
            destination: "Collecting_State",
            delay: "0ms"
        },
        {
            target: "CPU_Item",
            action: "Change to",
            destination: "Completed_State",
            delay: "1000ms"
        },
        {
            target: "Motherboard_Item",
            action: "Change to",
            destination: "Collecting_State",
            delay: "1200ms"
        },
        // ... 其他硬件项目
    ]
}

// 设备指纹显示动画
Fingerprint_Reveal: {
    trigger: "After Hardware Collection",
    action: "Type Text",
    text: "A1B2-C3D4-E5F6-G7H8-I9J0-K1L2-M3N4-O5P6",
    duration: "2000ms",
    easing: "Ease Out"
}

// 复制按钮交互
CopyButton_Click: {
    trigger: "On Click",
    action: "Copy to Clipboard",
    content: "Device Fingerprint",
    feedback: {
        action: "Show Tooltip",
        text: "已复制到剪贴板",
        duration: "2000ms"
    }
}
```

### 2.4 License验证页面
**页面布局：**
```
┌─────────────────────────────────────────────────────────────┐
│  [←] 步骤 3/4: 验证License                                  │
│                                                            │
│                    [🔄 验证图标]                           │
│                                                            │
│                正在验证License有效性...                     │
│                                                            │
│  ┌─────┐    ┌─────┐    ┌─────┐    ┌─────┐                │
│  │ ✅  │ →  │ 🔄  │ →  │ ⏳  │ →  │ ⏳  │                │
│  │文件 │    │指纹 │    │权限 │    │激活 │                │
│  │验证 │    │匹配 │    │检查 │    │确认 │                │
│  └─────┘    └─────┘    └─────┘    └─────┘                │
│                                                            │
│  验证进度: ████████░░░░░░░░░░░░ 40%                        │
│                                                            │
│  当前步骤: 正在匹配设备指纹...                              │
│  预计剩余时间: 约30秒                                       │
│                                                            │
│  验证详情:                                                  │
│  ✅ License文件格式正确                                     │
│  ✅ 数字签名验证通过                                        │
│  🔄 设备指纹匹配中...                                       │
│  ⏳ 权限信息解析等待中...                                   │
│                                                            │
│                              [取消验证]                    │
└─────────────────────────────────────────────────────────────┘
```

**交互规范：**
```javascript
// 验证进度动画
Verification_Progress: {
    trigger: "On Load",
    sequence: [
        {
            target: "Progress_Bar",
            property: "Width",
            from: "0%",
            to: "25%",
            duration: "3000ms",
            label: "文件验证"
        },
        {
            target: "Progress_Bar",
            property: "Width",
            from: "25%",
            to: "50%",
            duration: "5000ms",
            label: "指纹匹配"
        },
        {
            target: "Progress_Bar",
            property: "Width",
            from: "50%",
            to: "75%",
            duration: "4000ms",
            label: "权限检查"
        },
        {
            target: "Progress_Bar",
            property: "Width",
            from: "75%",
            to: "100%",
            duration: "2000ms",
            label: "激活确认"
        }
    ]
}

// 验证步骤状态更新
Step_Status_Update: {
    trigger: "Progress Change",
    conditions: [
        {
            when: "Progress >= 25%",
            action: "Change to",
            target: "File_Verification_Step",
            destination: "Completed_State"
        },
        {
            when: "Progress >= 50%",
            action: "Change to",
            target: "Fingerprint_Match_Step",
            destination: "Completed_State"
        }
        // ... 其他步骤
    ]
}

// 取消验证
Cancel_Verification: {
    trigger: "On Click",
    action: "Show Overlay",
    overlay: "Cancel_Confirm_Dialog",
    animation: "Fade In",
    duration: "200ms"
}
```

### 2.5 激活完成页面
**页面布局：**
```
┌─────────────────────────────────────────────────────────────┐
│  步骤 4/4: 激活完成                                         │
│                                                            │
│                    [✅ 成功图标]                           │
│                                                            │
│                License激活成功！                           │
│              欢迎使用 AirMonitor                            │
│                                                            │
│  ┌─────────────────────────────────────────────────────┐    │
│  │ 激活信息摘要                                        │    │
│  │                                                    │    │
│  │ 授权用户: 示例科技有限公司                          │    │
│  │ 用户角色: 系统工程师                               │    │
│  │ 激活时间: 2024年1月18日 14:30:25                   │    │
│  │ 有效期至: 2025年1月18日                            │    │
│  │ 设备指纹: A1B2-C3D4-E5F6-G7H8                     │    │
│  │                                                    │    │
│  │ 可用功能:                                          │    │
│  │ ✅ 设备连接管理                                    │    │
│  │ ✅ 实时监控面板                                    │    │
│  │ ✅ 设备控制中心                                    │    │
│  │ ✅ 数据分析工具                                    │    │
│  │ ✅ 参数管理中心                                    │    │
│  │ ✅ 故障诊断助手                                    │    │
│  │ ✅ 测试验证工具                                    │    │
│  │ ✅ 协作工作空间                                    │    │
│  └─────────────────────────────────────────────────────┘    │
│                                                            │
│                    [进入软件] [查看帮助]                   │
└─────────────────────────────────────────────────────────────┘
```

**交互规范：**
```javascript
// 成功页面进入动画
Success_Page_Enter: {
    trigger: "On Load",
    sequence: [
        {
            target: "Success_Icon",
            action: "Scale In",
            from: "0",
            to: "1",
            duration: "500ms",
            easing: "Bounce"
        },
        {
            target: "Success_Title",
            action: "Fade In Up",
            delay: "300ms",
            duration: "400ms"
        },
        {
            target: "Info_Card",
            action: "Slide In Up",
            delay: "600ms",
            duration: "500ms"
        }
    ]
}

// 进入软件按钮
EnterSoftware_Click: {
    trigger: "On Click",
    action: "Navigate to",
    destination: "Main_Application",
    animation: "Fade Out",
    duration: "500ms",
    then: {
        action: "Load Application",
        target: "Main Window"
    }
}

// 查看帮助按钮
ViewHelp_Click: {
    trigger: "On Click",
    action: "Open External",
    url: "help://activation-guide",
    animation: "Scale",
    duration: "150ms"
}
```

## 3. 错误处理原型

### 3.1 License文件错误
**错误页面布局：**
```
┌─────────────────────────────────────────────────────────────┐
│  [←] License文件验证失败                                    │
│                                                            │
│                    [❌ 错误图标]                           │
│                                                            │
│                License文件无效                             │
│                                                            │
│  错误详情:                                                  │
│  • 文件格式不正确或已损坏                                  │
│  • 数字签名验证失败                                        │
│  • License文件可能被篡改                                   │
│                                                            │
│  解决方案:                                                  │
│  1. 检查License文件是否完整                                │
│  2. 重新下载License文件                                    │
│  3. 联系技术支持获取帮助                                   │
│                                                            │
│              [重新选择文件] [联系支持] [返回]               │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 设备指纹不匹配错误
**错误页面布局：**
```
┌─────────────────────────────────────────────────────────────┐
│  [←] 设备指纹验证失败                                       │
│                                                            │
│                    [⚠️ 警告图标]                           │
│                                                            │
│              设备指纹不匹配                                 │
│                                                            │
│  当前设备指纹:                                              │
│  A1B2-C3D4-E5F6-G7H8-I9J0-K1L2-M3N4-O5P6                 │
│                                                            │
│  License授权指纹:                                           │
│  X1Y2-Z3A4-B5C6-D7E8-F9G0-H1I2-J3K4-L5M6                 │
│                                                            │
│  可能原因:                                                  │
│  • 硬件配置发生变化                                        │
│  • License文件不是为此设备生成                             │
│  • 系统重装或硬件更换                                      │
│                                                            │
│  解决方案:                                                  │
│  1. 使用正确的License文件                                  │
│  2. 申请设备指纹更新                                       │
│  3. 联系技术支持重新绑定                                   │
│                                                            │
│          [重新导入] [申请更新] [联系支持] [返回]            │
└─────────────────────────────────────────────────────────────┘
```

### 3.3 License过期错误
**错误页面布局：**
```
┌─────────────────────────────────────────────────────────────┐
│  [←] License已过期                                          │
│                                                            │
│                    [⏰ 过期图标]                           │
│                                                            │
│                License使用期限已过期                       │
│                                                            │
│  License信息:                                               │
│  • 授权用户: 示例科技有限公司                              │
│  • 激活时间: 2023年1月18日                                 │
│  • 过期时间: 2024年1月18日                                 │
│  • 当前时间: 2024年3月15日                                 │
│                                                            │
│  续费选项:                                                  │
│  • 标准版续费: ¥2,999/年                                   │
│  • 专业版升级: ¥4,999/年                                   │
│  • 企业版升级: ¥9,999/年                                   │
│                                                            │
│              [立即续费] [联系销售] [试用模式]               │
└─────────────────────────────────────────────────────────────┘
```

## 4. 原型交互流程图

### 4.1 正常激活流程
```
开始激活 → 文件导入 → 指纹生成 → License验证 → 激活完成
    ↓         ↓         ↓          ↓          ↓
  流程介绍   文件上传   硬件扫描    验证进度    权限确认
    ↓         ↓         ↓          ↓          ↓
  步骤说明   进度反馈   指纹显示    状态更新    进入软件
```

### 4.2 错误处理流程
```
文件导入 → 文件验证失败 → 错误提示 → 重新选择 → 继续流程
    ↓           ↓           ↓         ↓         ↓
指纹生成 → 指纹不匹配 → 错误说明 → 申请更新 → 技术支持
    ↓           ↓           ↓         ↓         ↓
License验证 → 验证失败 → 失败原因 → 解决方案 → 重新验证
```

### 4.3 用户决策点
```
激活欢迎页 → [开始激活 / 稍后激活]
文件导入页 → [选择文件 / 拖拽文件 / 返回]
错误处理页 → [重试 / 联系支持 / 返回]
激活完成页 → [进入软件 / 查看帮助]
```

## 5. 原型测试计划

### 5.1 用户测试任务
**任务1: 完整激活流程**
- 目标: 用户能够顺利完成License激活
- 步骤: 从欢迎页开始，完成整个激活流程
- 成功标准: 5分钟内完成激活，无重大困惑

**任务2: 错误恢复测试**
- 目标: 用户能够理解错误信息并采取正确行动
- 步骤: 模拟各种错误情况，观察用户反应
- 成功标准: 用户能理解错误原因并知道如何解决

**任务3: 设备指纹理解**
- 目标: 用户能够理解设备指纹的作用和重要性
- 步骤: 在指纹生成页面询问用户理解程度
- 成功标准: 用户能正确解释设备指纹的用途

### 5.2 可用性评估指标
- **任务完成率**: 用户成功完成激活的比例
- **任务完成时间**: 完成激活流程所需的平均时间
- **错误率**: 用户在操作过程中的错误次数
- **满意度**: 用户对激活流程的满意度评分
- **理解度**: 用户对关键概念的理解程度

### 5.3 原型迭代计划
**第一轮测试后优化:**
- 根据用户反馈调整界面布局
- 优化错误信息的表达方式
- 改进设备指纹的说明文案
- 调整动画时长和效果

**第二轮测试后优化:**
- 细化交互细节和微动效
- 完善无障碍访问支持
- 优化不同屏幕尺寸的适配
- 最终确定设计规范
