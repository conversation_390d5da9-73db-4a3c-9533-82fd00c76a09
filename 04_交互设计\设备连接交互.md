# AirMonitor 设备连接交互设计

## 1. 连接流程交互设计

### 1.1 连接向导交互
**新建连接向导：**
```javascript
// 连接向导状态管理
class ConnectionWizard {
    constructor() {
        this.currentStep = 1;
        this.totalSteps = 4;
        this.connectionData = {};
        this.init();
    }

    init() {
        this.renderWizard();
        this.bindEvents();
    }

    renderWizard() {
        const steps = [
            { id: 1, title: '选择连接类型', icon: 'plug' },
            { id: 2, title: '配置连接参数', icon: 'settings' },
            { id: 3, title: '测试连接', icon: 'test-tube' },
            { id: 4, title: '保存连接', icon: 'save' }
        ];

        // 渲染步骤指示器
        this.renderStepIndicator(steps);

        // 渲染当前步骤内容
        this.renderStepContent();
    }

    nextStep() {
        if (this.validateCurrentStep()) {
            this.currentStep++;
            this.updateWizard();
        }
    }

    previousStep() {
        if (this.currentStep > 1) {
            this.currentStep--;
            this.updateWizard();
        }
    }
}
```

**步骤指示器样式：**
```css
/* 连接向导容器 */
.connection-wizard {
    background: var(--color-background);
    border-radius: 8px;
    box-shadow: var(--shadow-elevation-4);
    overflow: hidden;
    max-width: 600px;
    margin: 0 auto;
}

/* 步骤指示器 */
.wizard-steps {
    display: flex;
    background: var(--color-surface);
    padding: 16px 24px;
    border-bottom: 1px solid var(--color-border);
}

.wizard-step {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
}

.wizard-step:not(:last-child)::after {
    content: '';
    position: absolute;
    right: -50%;
    top: 50%;
    width: 100%;
    height: 2px;
    background: var(--color-border);
    transform: translateY(-50%);
    z-index: 1;
}

.wizard-step.completed::after {
    background: var(--color-success);
}

.wizard-step.active::after {
    background: linear-gradient(to right, var(--color-primary) 50%, var(--color-border) 50%);
}

/* 步骤图标 */
.wizard-step-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--color-border);
    color: var(--color-text-tertiary);
    font-size: 14px;
    transition: all 250ms cubic-bezier(0.4, 0.0, 0.2, 1);
    z-index: 2;
    position: relative;
}

.wizard-step.active .wizard-step-icon {
    background: var(--color-primary);
    color: #FFFFFF;
    transform: scale(1.1);
}

.wizard-step.completed .wizard-step-icon {
    background: var(--color-success);
    color: #FFFFFF;
}

.wizard-step.completed .wizard-step-icon::before {
    content: '✓';
    font-size: 16px;
}

/* 步骤标题 */
.wizard-step-title {
    font: 12px/16px Segoe UI, semibold;
    color: var(--color-text-secondary);
    transition: color 150ms;
}

.wizard-step.active .wizard-step-title {
    color: var(--color-primary);
}

.wizard-step.completed .wizard-step-title {
    color: var(--color-success);
}
```

### 1.2 连接参数配置交互
**串口参数配置：**
```css
/* 参数配置表单 */
.connection-config {
    padding: 24px;
}

.config-section {
    margin-bottom: 24px;
}

.config-section-title {
    font: 16px/24px Segoe UI, semibold;
    color: var(--color-text-primary);
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.config-section-icon {
    width: 16px;
    height: 16px;
    color: var(--color-primary);
}

/* 参数输入组 */
.config-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 16px;
}

.config-group.single {
    grid-template-columns: 1fr;
}

.config-field {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.config-label {
    font: 12px/16px Segoe UI, semibold;
    color: var(--color-text-primary);
}

.config-description {
    font: 11px/14px Segoe UI;
    color: var(--color-text-tertiary);
    margin-top: 2px;
}

/* 串口选择下拉框 */
.port-selector {
    position: relative;
}

.port-dropdown {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border: 1px solid var(--color-border);
    border-radius: 4px;
    background: var(--color-background);
    cursor: pointer;
    transition: all 150ms;
}

.port-dropdown:hover {
    border-color: var(--color-primary);
}

.port-dropdown.open {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 1px var(--color-primary);
}

.port-icon {
    width: 16px;
    height: 16px;
    color: var(--color-text-secondary);
}

.port-name {
    flex: 1;
    font: 14px/20px Segoe UI;
    color: var(--color-text-primary);
}

.port-status {
    font: 11px/14px Segoe UI;
    color: var(--color-text-tertiary);
    padding: 2px 6px;
    border-radius: 2px;
    background: var(--color-surface);
}

.port-status.available {
    color: var(--color-success);
    background: rgba(16, 124, 16, 0.1);
}

.port-status.busy {
    color: var(--color-warning);
    background: rgba(255, 185, 0, 0.1);
}

/* 刷新按钮 */
.port-refresh {
    width: 24px;
    height: 24px;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--color-text-secondary);
    transition: all 150ms;
}

.port-refresh:hover {
    background: var(--color-surface);
    color: var(--color-primary);
}

.port-refresh.refreshing {
    animation: port-refresh-spin 1s linear infinite;
}

@keyframes port-refresh-spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
```

### 1.3 连接测试交互
**连接测试界面：**
```css
/* 连接测试区域 */
.connection-test {
    padding: 24px;
    text-align: center;
}

.test-status {
    margin-bottom: 24px;
}

.test-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    transition: all 250ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

.test-icon.testing {
    background: var(--color-surface);
    color: var(--color-primary);
    animation: test-pulse 2s infinite;
}

.test-icon.success {
    background: var(--color-success);
    color: #FFFFFF;
    animation: test-success 0.5s ease-out;
}

.test-icon.error {
    background: var(--color-error);
    color: #FFFFFF;
    animation: test-error 0.5s ease-out;
}

@keyframes test-pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

@keyframes test-success {
    0% { transform: scale(0.8); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

@keyframes test-error {
    0%, 20%, 40%, 60%, 80% { transform: translateX(-5px); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(5px); }
    100% { transform: translateX(0); }
}

.test-message {
    font: 16px/24px Segoe UI, semibold;
    color: var(--color-text-primary);
    margin-bottom: 8px;
}

.test-details {
    font: 14px/20px Segoe UI;
    color: var(--color-text-secondary);
    margin-bottom: 24px;
}

/* 测试进度 */
.test-progress {
    margin-bottom: 24px;
}

.test-steps {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-bottom: 16px;
}

.test-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    min-width: 80px;
}

.test-step-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--color-surface);
    color: var(--color-text-tertiary);
    font-size: 14px;
    transition: all 250ms;
}

.test-step.active .test-step-icon {
    background: var(--color-primary);
    color: #FFFFFF;
    animation: test-step-active 1s infinite;
}

.test-step.completed .test-step-icon {
    background: var(--color-success);
    color: #FFFFFF;
}

.test-step.error .test-step-icon {
    background: var(--color-error);
    color: #FFFFFF;
}

@keyframes test-step-active {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.test-step-label {
    font: 11px/14px Segoe UI;
    color: var(--color-text-secondary);
    text-align: center;
}

.test-step.active .test-step-label {
    color: var(--color-primary);
    font-weight: 600;
}
```

## 2. 连接状态管理交互

### 2.1 连接状态指示器
**状态指示器设计：**
```css
/* 连接状态指示器 */
.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 4px;
    font: 12px/16px Segoe UI, semibold;
    transition: all 150ms;
}

.connection-status.connected {
    background: rgba(16, 124, 16, 0.1);
    color: var(--color-success);
    border: 1px solid rgba(16, 124, 16, 0.2);
}

.connection-status.connecting {
    background: rgba(255, 185, 0, 0.1);
    color: var(--color-warning);
    border: 1px solid rgba(255, 185, 0, 0.2);
}

.connection-status.disconnected {
    background: rgba(209, 52, 56, 0.1);
    color: var(--color-error);
    border: 1px solid rgba(209, 52, 56, 0.2);
}

.connection-status.error {
    background: rgba(209, 52, 56, 0.15);
    color: var(--color-error);
    border: 1px solid rgba(209, 52, 56, 0.3);
}

/* 状态指示灯 */
.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.connection-status.connected .status-indicator {
    background: var(--color-success);
    box-shadow: 0 0 4px rgba(16, 124, 16, 0.5);
}

.connection-status.connecting .status-indicator {
    background: var(--color-warning);
    animation: status-pulse 1.5s infinite;
}

.connection-status.disconnected .status-indicator {
    background: var(--color-text-tertiary);
}

.connection-status.error .status-indicator {
    background: var(--color-error);
    animation: status-blink 1s infinite;
}

@keyframes status-pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.2); }
}

@keyframes status-blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* 状态文本 */
.status-text {
    white-space: nowrap;
}

/* 连接时间 */
.connection-time {
    font: 10px/12px Segoe UI;
    color: var(--color-text-tertiary);
    margin-left: auto;
}
```

### 2.2 连接质量监控
**连接质量指示器：**
```css
/* 连接质量指示器 */
.connection-quality {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: var(--color-surface);
    border-radius: 4px;
    margin-top: 8px;
}

.quality-label {
    font: 11px/14px Segoe UI, semibold;
    color: var(--color-text-secondary);
}

.quality-bars {
    display: flex;
    gap: 2px;
    align-items: end;
}

.quality-bar {
    width: 3px;
    background: var(--color-border);
    border-radius: 1px;
    transition: background 250ms;
}

.quality-bar:nth-child(1) { height: 4px; }
.quality-bar:nth-child(2) { height: 6px; }
.quality-bar:nth-child(3) { height: 8px; }
.quality-bar:nth-child(4) { height: 10px; }
.quality-bar:nth-child(5) { height: 12px; }

/* 质量等级样式 */
.connection-quality.excellent .quality-bar {
    background: var(--color-success);
}

.connection-quality.good .quality-bar:nth-child(-n+4) {
    background: var(--color-success);
}

.connection-quality.fair .quality-bar:nth-child(-n+3) {
    background: var(--color-warning);
}

.connection-quality.poor .quality-bar:nth-child(-n+2) {
    background: var(--color-error);
}

.connection-quality.very-poor .quality-bar:nth-child(1) {
    background: var(--color-error);
}

/* 质量数值 */
.quality-value {
    font: 11px/14px Segoe UI;
    color: var(--color-text-tertiary);
    margin-left: auto;
}
```

## 3. 连接操作交互

### 3.1 连接/断开操作
**连接按钮交互：**
```css
/* 连接操作按钮 */
.connection-action {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 4px;
    font: 14px/20px Segoe UI, semibold;
    cursor: pointer;
    transition: all 150ms cubic-bezier(0.4, 0.0, 0.2, 1);
    border: none;
    position: relative;
    overflow: hidden;
}

.connection-action.connect {
    background: var(--color-success);
    color: #FFFFFF;
}

.connection-action.connect:hover {
    background: #0E6B0E;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(16, 124, 16, 0.3);
}

.connection-action.disconnect {
    background: var(--color-error);
    color: #FFFFFF;
}

.connection-action.disconnect:hover {
    background: #B02E32;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(209, 52, 56, 0.3);
}

.connection-action.connecting {
    background: var(--color-warning);
    color: #FFFFFF;
    pointer-events: none;
}

/* 按钮图标 */
.action-icon {
    width: 16px;
    height: 16px;
    transition: transform 150ms;
}

.connection-action.connecting .action-icon {
    animation: action-spin 1s linear infinite;
}

@keyframes action-spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 按钮文本 */
.action-text {
    white-space: nowrap;
}

/* 连接进度效果 */
.connection-action.connecting::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: action-progress 1.5s infinite;
}

@keyframes action-progress {
    0% { left: -100%; }
    100% { left: 100%; }
}
```

### 3.2 连接历史管理
**连接历史列表：**
```css
/* 连接历史容器 */
.connection-history {
    background: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: 8px;
    overflow: hidden;
}

.history-header {
    padding: 16px 20px;
    background: var(--color-surface);
    border-bottom: 1px solid var(--color-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.history-title {
    font: 16px/24px Segoe UI, semibold;
    color: var(--color-text-primary);
}

.history-actions {
    display: flex;
    gap: 8px;
}

/* 历史记录项 */
.history-item {
    padding: 12px 20px;
    border-bottom: 1px solid var(--color-surface);
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: all 150ms;
}

.history-item:hover {
    background: var(--color-surface);
}

.history-item:last-child {
    border-bottom: none;
}

.history-item.active {
    background: rgba(0, 120, 212, 0.1);
    border-left: 3px solid var(--color-primary);
}

/* 连接图标 */
.history-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--color-surface);
    color: var(--color-text-secondary);
    flex-shrink: 0;
}

.history-item.success .history-icon {
    background: rgba(16, 124, 16, 0.1);
    color: var(--color-success);
}

.history-item.error .history-icon {
    background: rgba(209, 52, 56, 0.1);
    color: var(--color-error);
}

/* 连接信息 */
.history-info {
    flex: 1;
    min-width: 0;
}

.history-name {
    font: 14px/20px Segoe UI, semibold;
    color: var(--color-text-primary);
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.history-details {
    font: 12px/16px Segoe UI;
    color: var(--color-text-secondary);
    display: flex;
    gap: 12px;
}

.history-port {
    display: flex;
    align-items: center;
    gap: 4px;
}

.history-time {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* 连接操作 */
.history-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 150ms;
}

.history-item:hover .history-actions {
    opacity: 1;
}

.history-action {
    width: 24px;
    height: 24px;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--color-text-secondary);
    transition: all 150ms;
}

.history-action:hover {
    background: var(--color-surface);
    color: var(--color-primary);
}

.history-action.delete:hover {
    background: rgba(209, 52, 56, 0.1);
    color: var(--color-error);
}
```