# AirMonitor 实时监控交互设计

## 1. 监控面板交互设计

### 1.1 仪表板布局交互
**响应式仪表板：**
```css
/* 监控仪表板容器 */
.monitoring-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 16px;
    padding: 16px;
    height: 100%;
    overflow-y: auto;
}

/* 监控卡片 */
.monitor-card {
    background: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: 8px;
    overflow: hidden;
    transition: all 250ms cubic-bezier(0.4, 0.0, 0.2, 1);
    position: relative;
}

.monitor-card:hover {
    box-shadow: var(--shadow-elevation-4);
    transform: translateY(-2px);
}

.monitor-card.alert {
    border-color: var(--color-error);
    animation: card-alert-pulse 2s infinite;
}

.monitor-card.warning {
    border-color: var(--color-warning);
}

.monitor-card.normal {
    border-color: var(--color-success);
}

@keyframes card-alert-pulse {
    0%, 100% { box-shadow: 0 0 0 0 rgba(209, 52, 56, 0.4); }
    50% { box-shadow: 0 0 0 4px rgba(209, 52, 56, 0.1); }
}

/* 卡片大小变体 */
.monitor-card.large {
    grid-column: span 2;
}

.monitor-card.tall {
    grid-row: span 2;
}

.monitor-card.full-width {
    grid-column: 1 / -1;
}
```

### 1.2 实时数据更新交互
**数据更新动画：**
```css
/* 数据值容器 */
.data-value {
    font: 24px/32px Segoe UI, semibold;
    color: var(--color-text-primary);
    transition: all 150ms cubic-bezier(0.4, 0.0, 0.2, 1);
    position: relative;
}

/* 数据更新闪烁效果 */
.data-value.updating {
    animation: data-update-flash 0.3s ease-out;
}

@keyframes data-update-flash {
    0% { background: transparent; }
    50% { background: rgba(0, 120, 212, 0.1); }
    100% { background: transparent; }
}

/* 数据变化指示器 */
.data-change {
    position: absolute;
    top: 0;
    right: -20px;
    font-size: 12px;
    opacity: 0;
    transform: translateY(5px);
    transition: all 250ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

.data-change.show {
    opacity: 1;
    transform: translateY(0);
}

.data-change.increase {
    color: var(--color-success);
}

.data-change.decrease {
    color: var(--color-error);
}

.data-change::before {
    content: '↑';
}

.data-change.decrease::before {
    content: '↓';
}

/* 数据趋势图标 */
.data-trend {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font: 12px/16px Segoe UI;
    color: var(--color-text-secondary);
    margin-top: 4px;
}

.trend-icon {
    width: 12px;
    height: 12px;
    transition: transform 150ms;
}

.data-trend.up .trend-icon {
    color: var(--color-success);
    transform: rotate(-45deg);
}

.data-trend.down .trend-icon {
    color: var(--color-error);
    transform: rotate(45deg);
}

.data-trend.stable .trend-icon {
    color: var(--color-text-tertiary);
    transform: rotate(0deg);
}
```

### 1.3 参数阈值交互
**阈值指示器：**
```css
/* 参数阈值容器 */
.parameter-threshold {
    position: relative;
    margin-top: 12px;
}

/* 阈值进度条 */
.threshold-bar {
    height: 8px;
    background: var(--color-surface);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.threshold-fill {
    height: 100%;
    border-radius: 4px;
    transition: all 250ms cubic-bezier(0.4, 0.0, 0.2, 1);
    position: relative;
}

.threshold-fill.normal {
    background: linear-gradient(90deg, var(--color-success), #4CAF50);
}

.threshold-fill.warning {
    background: linear-gradient(90deg, var(--color-warning), #FFC107);
}

.threshold-fill.critical {
    background: linear-gradient(90deg, var(--color-error), #F44336);
    animation: threshold-critical-pulse 1s infinite;
}

@keyframes threshold-critical-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* 阈值标记 */
.threshold-markers {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    pointer-events: none;
}

.threshold-marker {
    position: absolute;
    top: -2px;
    width: 2px;
    height: 12px;
    background: var(--color-text-primary);
    border-radius: 1px;
}

.threshold-marker.warning {
    background: var(--color-warning);
}

.threshold-marker.critical {
    background: var(--color-error);
}

/* 阈值标签 */
.threshold-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 4px;
    font: 10px/12px Segoe UI;
    color: var(--color-text-tertiary);
}
```

## 2. 报警系统交互

### 2.1 报警通知交互
**报警弹出通知：**
```css
/* 报警通知容器 */
.alarm-notifications {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 2000;
    max-width: 400px;
    pointer-events: none;
}

/* 报警通知项 */
.alarm-notification {
    background: var(--color-background);
    border: 1px solid var(--color-border);
    border-left: 4px solid var(--color-error);
    border-radius: 4px;
    box-shadow: var(--shadow-elevation-8);
    margin-bottom: 8px;
    padding: 16px;
    pointer-events: all;
    transform: translateX(100%);
    opacity: 0;
    animation: alarm-slide-in 0.3s cubic-bezier(0.4, 0.0, 0.2, 1) forwards;
}

.alarm-notification.warning {
    border-left-color: var(--color-warning);
}

.alarm-notification.info {
    border-left-color: var(--color-info);
}

@keyframes alarm-slide-in {
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 报警通知头部 */
.alarm-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.alarm-icon {
    width: 16px;
    height: 16px;
    color: var(--color-error);
}

.alarm-title {
    font: 14px/20px Segoe UI, semibold;
    color: var(--color-text-primary);
    flex: 1;
}

.alarm-time {
    font: 11px/14px Segoe UI;
    color: var(--color-text-tertiary);
}

.alarm-close {
    width: 16px;
    height: 16px;
    cursor: pointer;
    color: var(--color-text-secondary);
    transition: color 150ms;
}

.alarm-close:hover {
    color: var(--color-text-primary);
}

/* 报警内容 */
.alarm-content {
    font: 12px/16px Segoe UI;
    color: var(--color-text-secondary);
    margin-bottom: 12px;
}

/* 报警操作 */
.alarm-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.alarm-action {
    padding: 4px 8px;
    border-radius: 2px;
    font: 11px/14px Segoe UI, semibold;
    cursor: pointer;
    transition: all 150ms;
}

.alarm-action.primary {
    background: var(--color-primary);
    color: #FFFFFF;
}

.alarm-action.primary:hover {
    background: var(--color-primary-hover);
}

.alarm-action.secondary {
    background: var(--color-surface);
    color: var(--color-text-primary);
}

.alarm-action.secondary:hover {
    background: var(--color-surface-secondary);
}
```

### 2.2 报警列表交互
**报警历史列表：**
```css
/* 报警列表容器 */
.alarm-list {
    background: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: 8px;
    overflow: hidden;
}

/* 报警列表头部 */
.alarm-list-header {
    padding: 16px 20px;
    background: var(--color-surface);
    border-bottom: 1px solid var(--color-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.alarm-list-title {
    font: 16px/24px Segoe UI, semibold;
    color: var(--color-text-primary);
}

.alarm-filters {
    display: flex;
    gap: 8px;
}

.alarm-filter {
    padding: 4px 8px;
    border-radius: 2px;
    font: 11px/14px Segoe UI, semibold;
    cursor: pointer;
    transition: all 150ms;
    border: 1px solid var(--color-border);
    background: var(--color-background);
}

.alarm-filter.active {
    background: var(--color-primary);
    color: #FFFFFF;
    border-color: var(--color-primary);
}

.alarm-filter:hover:not(.active) {
    background: var(--color-surface);
}

/* 报警项 */
.alarm-item {
    padding: 12px 20px;
    border-bottom: 1px solid var(--color-surface);
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: all 150ms;
    position: relative;
}

.alarm-item:hover {
    background: var(--color-surface);
}

.alarm-item:last-child {
    border-bottom: none;
}

.alarm-item.unread {
    background: rgba(0, 120, 212, 0.05);
}

.alarm-item.unread::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--color-primary);
}

/* 报警严重程度指示器 */
.alarm-severity {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.alarm-severity.critical {
    background: var(--color-error);
    box-shadow: 0 0 4px rgba(209, 52, 56, 0.5);
    animation: severity-critical-blink 1s infinite;
}

.alarm-severity.warning {
    background: var(--color-warning);
}

.alarm-severity.info {
    background: var(--color-info);
}

@keyframes severity-critical-blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* 报警信息 */
.alarm-info {
    flex: 1;
    min-width: 0;
}

.alarm-message {
    font: 14px/20px Segoe UI;
    color: var(--color-text-primary);
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.alarm-details {
    font: 12px/16px Segoe UI;
    color: var(--color-text-secondary);
    display: flex;
    gap: 12px;
}

.alarm-source {
    display: flex;
    align-items: center;
    gap: 4px;
}

.alarm-timestamp {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* 报警状态 */
.alarm-status {
    padding: 2px 6px;
    border-radius: 2px;
    font: 10px/12px Segoe UI, semibold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.alarm-status.active {
    background: rgba(209, 52, 56, 0.1);
    color: var(--color-error);
}

.alarm-status.acknowledged {
    background: rgba(255, 185, 0, 0.1);
    color: var(--color-warning);
}

.alarm-status.resolved {
    background: rgba(16, 124, 16, 0.1);
    color: var(--color-success);
}
```

## 3. 数据可视化交互

### 3.1 实时图表交互
**图表悬停交互：**
```css
/* 图表容器 */
.chart-container {
    position: relative;
    background: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: 8px;
    padding: 16px;
    overflow: hidden;
}

/* 图表工具栏 */
.chart-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--color-surface);
}

.chart-title {
    font: 16px/24px Segoe UI, semibold;
    color: var(--color-text-primary);
}

.chart-controls {
    display: flex;
    gap: 8px;
}

.chart-control {
    width: 24px;
    height: 24px;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--color-text-secondary);
    transition: all 150ms;
}

.chart-control:hover {
    background: var(--color-surface);
    color: var(--color-primary);
}

.chart-control.active {
    background: var(--color-primary);
    color: #FFFFFF;
}

/* 图表主体 */
.chart-main {
    position: relative;
    height: 200px;
    overflow: hidden;
}

/* 图表工具提示 */
.chart-tooltip {
    position: absolute;
    background: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: 4px;
    padding: 8px 12px;
    font: 12px/16px Segoe UI;
    box-shadow: var(--shadow-elevation-4);
    pointer-events: none;
    opacity: 0;
    transform: translateY(-5px);
    transition: all 150ms cubic-bezier(0.4, 0.0, 0.2, 1);
    z-index: 1000;
}

.chart-tooltip.show {
    opacity: 1;
    transform: translateY(0);
}

.tooltip-title {
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: 4px;
}

.tooltip-value {
    color: var(--color-primary);
    font-weight: 600;
}

.tooltip-time {
    color: var(--color-text-tertiary);
    font-size: 10px;
    margin-top: 2px;
}

/* 图表十字线 */
.chart-crosshair {
    position: absolute;
    pointer-events: none;
    opacity: 0;
    transition: opacity 150ms;
}

.chart-crosshair.show {
    opacity: 1;
}

.crosshair-line {
    position: absolute;
    background: var(--color-primary);
    opacity: 0.3;
}

.crosshair-vertical {
    width: 1px;
    height: 100%;
}

.crosshair-horizontal {
    width: 100%;
    height: 1px;
}
```

### 3.2 数据缩放和平移
**图表缩放交互：**
```javascript
// 图表缩放和平移控制
class ChartInteraction {
    constructor(chartElement) {
        this.chart = chartElement;
        this.isZooming = false;
        this.isPanning = false;
        this.zoomLevel = 1;
        this.panOffset = { x: 0, y: 0 };
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.createZoomControls();
    }
    
    bindEvents() {
        // 鼠标滚轮缩放
        this.chart.addEventListener('wheel', this.handleWheel.bind(this));
        
        // 鼠标拖拽平移
        this.chart.addEventListener('mousedown', this.handleMouseDown.bind(this));
        this.chart.addEventListener('mousemove', this.handleMouseMove.bind(this));
        this.chart.addEventListener('mouseup', this.handleMouseUp.bind(this));
        
        // 双击重置
        this.chart.addEventListener('dblclick', this.handleDoubleClick.bind(this));
    }
    
    handleWheel(event) {
        event.preventDefault();
        
        const delta = event.deltaY > 0 ? 0.9 : 1.1;
        const newZoomLevel = Math.max(0.5, Math.min(5, this.zoomLevel * delta));
        
        if (newZoomLevel !== this.zoomLevel) {
            this.zoomLevel = newZoomLevel;
            this.updateChartTransform();
            this.updateZoomControls();
        }
    }
    
    handleMouseDown(event) {
        if (event.button === 0) { // 左键
            this.isPanning = true;
            this.lastMousePos = { x: event.clientX, y: event.clientY };
            this.chart.style.cursor = 'grabbing';
        }
    }
    
    handleMouseMove(event) {
        if (this.isPanning) {
            const deltaX = event.clientX - this.lastMousePos.x;
            const deltaY = event.clientY - this.lastMousePos.y;
            
            this.panOffset.x += deltaX;
            this.panOffset.y += deltaY;
            
            this.updateChartTransform();
            this.lastMousePos = { x: event.clientX, y: event.clientY };
        }
    }
    
    handleMouseUp() {
        this.isPanning = false;
        this.chart.style.cursor = 'default';
    }
    
    handleDoubleClick() {
        this.zoomLevel = 1;
        this.panOffset = { x: 0, y: 0 };
        this.updateChartTransform();
        this.updateZoomControls();
    }
    
    updateChartTransform() {
        const transform = `translate(${this.panOffset.x}px, ${this.panOffset.y}px) scale(${this.zoomLevel})`;
        this.chart.querySelector('.chart-content').style.transform = transform;
    }
    
    createZoomControls() {
        const controls = document.createElement('div');
        controls.className = 'chart-zoom-controls';
        controls.innerHTML = `
            <button class="zoom-in" title="放大">+</button>
            <span class="zoom-level">100%</span>
            <button class="zoom-out" title="缩小">-</button>
            <button class="zoom-reset" title="重置">⌂</button>
        `;
        
        this.chart.appendChild(controls);
        this.bindZoomControls(controls);
    }
}
```

## 4. 监控配置交互

### 4.1 监控参数配置
**参数配置面板：**
```css
/* 监控配置面板 */
.monitor-config {
    background: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: 8px;
    overflow: hidden;
}

.config-header {
    padding: 16px 20px;
    background: var(--color-surface);
    border-bottom: 1px solid var(--color-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.config-title {
    font: 16px/24px Segoe UI, semibold;
    color: var(--color-text-primary);
}

.config-actions {
    display: flex;
    gap: 8px;
}

/* 配置项 */
.config-item {
    padding: 16px 20px;
    border-bottom: 1px solid var(--color-surface);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.config-item:last-child {
    border-bottom: none;
}

.config-info {
    flex: 1;
}

.config-name {
    font: 14px/20px Segoe UI, semibold;
    color: var(--color-text-primary);
    margin-bottom: 2px;
}

.config-description {
    font: 12px/16px Segoe UI;
    color: var(--color-text-secondary);
}

/* 配置控件 */
.config-control {
    margin-left: 16px;
}

/* 开关控件 */
.config-switch {
    position: relative;
    width: 44px;
    height: 24px;
    background: var(--color-border);
    border-radius: 12px;
    cursor: pointer;
    transition: background 150ms;
}

.config-switch.on {
    background: var(--color-primary);
}

.config-switch::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: #FFFFFF;
    border-radius: 50%;
    transition: transform 150ms cubic-bezier(0.4, 0.0, 0.2, 1);
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.config-switch.on::after {
    transform: translateX(20px);
}

/* 数值输入 */
.config-number {
    width: 80px;
    padding: 4px 8px;
    border: 1px solid var(--color-border);
    border-radius: 2px;
    font: 12px/16px Segoe UI;
    text-align: center;
}

.config-number:focus {
    border-color: var(--color-primary);
    outline: none;
}

/* 选择器 */
.config-select {
    min-width: 120px;
    padding: 4px 8px;
    border: 1px solid var(--color-border);
    border-radius: 2px;
    font: 12px/16px Segoe UI;
    background: var(--color-background);
}

.config-select:focus {
    border-color: var(--color-primary);
    outline: none;
}
```
