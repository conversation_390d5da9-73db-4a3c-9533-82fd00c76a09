# AirMonitor 主题切换设计规范

## 1. 主题系统架构

### 1.1 主题切换机制
**主题类型：**
- **明亮主题 (Light Theme)**：默认主题，适合日间使用
- **暗色主题 (Dark Theme)**：护眼主题，适合夜间或长时间使用
- **高对比度主题 (High Contrast)**：无障碍主题，适合视觉障碍用户
- **自动主题 (Auto Theme)**：根据系统设置自动切换

### 1.2 主题切换实现方式
**CSS变量系统：**
```css
:root {
    /* 明亮主题变量 */
    --color-primary: #0078D4;
    --color-background: #FFFFFF;
    --color-surface: #FAF9F8;
    --color-text-primary: #323130;
    --color-text-secondary: #605E5C;
    --color-border: #EDEBE9;
    --shadow-elevation-2: 0 2px 4px rgba(0,0,0,0.133);
}

[data-theme="dark"] {
    /* 暗色主题变量 */
    --color-primary: #4CC2FF;
    --color-background: #1F1F1F;
    --color-surface: #2D2D2D;
    --color-text-primary: #FFFFFF;
    --color-text-secondary: #E1E1E1;
    --color-border: #484848;
    --shadow-elevation-2: 0 2px 4px rgba(0,0,0,0.4);
}

[data-theme="high-contrast"] {
    /* 高对比度主题变量 */
    --color-primary: #FFFF00;
    --color-background: #000000;
    --color-surface: #000000;
    --color-text-primary: #FFFFFF;
    --color-text-secondary: #FFFFFF;
    --color-border: #FFFFFF;
    --shadow-elevation-2: 0 2px 4px rgba(255,255,255,0.3);
}
```

### 1.3 主题切换控件设计
**主题切换器位置：**
- **主要位置**：系统设置页面的显示设置区域
- **快捷位置**：顶部工具栏右侧的快捷切换按钮
- **状态栏**：底部状态栏显示当前主题状态

**切换器样式：**
```css
/* 主题切换按钮 */
.theme-switcher {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 150ms;
}

.theme-switcher:hover {
    background: var(--color-surface);
}

/* 主题图标 */
.theme-icon {
    width: 16px;
    height: 16px;
    color: var(--color-text-secondary);
}

/* 主题文字 */
.theme-text {
    font: 12px/16px Segoe UI;
    color: var(--color-text-secondary);
    white-space: nowrap;
}

/* 主题下拉菜单 */
.theme-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: 4px;
    box-shadow: var(--shadow-elevation-2);
    min-width: 160px;
    z-index: 1000;
}

.theme-option {
    padding: 8px 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font: 14px/20px Segoe UI;
    color: var(--color-text-primary);
}

.theme-option:hover {
    background: var(--color-surface);
}

.theme-option.active {
    background: var(--color-primary);
    color: #FFFFFF;
}
```

## 2. 明亮主题设计规范

### 2.1 明亮主题色彩定义
**主色彩系统：**
```css
/* 明亮主题完整色彩变量 */
:root {
    /* 品牌色彩 */
    --color-primary: #0078D4;
    --color-primary-hover: #106EBE;
    --color-primary-active: #005A9E;
    --color-secondary: #6B69D6;
    --color-accent: #FF8C00;
    
    /* 功能色彩 */
    --color-success: #107C10;
    --color-warning: #FFB900;
    --color-error: #D13438;
    --color-info: #0078D4;
    
    /* 背景色彩 */
    --color-background: #FFFFFF;
    --color-surface: #FAF9F8;
    --color-surface-secondary: #F3F2F1;
    --color-surface-tertiary: #EDEBE9;
    
    /* 文本色彩 */
    --color-text-primary: #323130;
    --color-text-secondary: #605E5C;
    --color-text-tertiary: #8A8886;
    --color-text-disabled: #C8C6C4;
    
    /* 边框色彩 */
    --color-border: #EDEBE9;
    --color-border-focus: #0078D4;
    --color-border-error: #D13438;
    
    /* 阴影 */
    --shadow-elevation-1: 0 1px 2px rgba(0,0,0,0.133);
    --shadow-elevation-2: 0 2px 4px rgba(0,0,0,0.133);
    --shadow-elevation-4: 0 4px 8px rgba(0,0,0,0.133);
    --shadow-elevation-8: 0 8px 16px rgba(0,0,0,0.133);
    --shadow-elevation-16: 0 16px 32px rgba(0,0,0,0.133);
}
```

### 2.2 明亮主题组件样式
**按钮明亮主题：**
```css
/* 主要按钮 */
.button.primary {
    background: var(--color-primary);
    color: #FFFFFF;
    border: none;
    box-shadow: var(--shadow-elevation-1);
}

.button.primary:hover {
    background: var(--color-primary-hover);
    box-shadow: var(--shadow-elevation-2);
}

.button.primary:active {
    background: var(--color-primary-active);
    box-shadow: var(--shadow-elevation-1);
}

/* 次要按钮 */
.button.secondary {
    background: transparent;
    color: var(--color-primary);
    border: 1px solid var(--color-primary);
}

.button.secondary:hover {
    background: var(--color-surface);
    border-color: var(--color-primary-hover);
    color: var(--color-primary-hover);
}
```

**卡片明亮主题：**
```css
.card {
    background: var(--color-background);
    border: 1px solid var(--color-border);
    box-shadow: var(--shadow-elevation-2);
    color: var(--color-text-primary);
}

.card-header {
    border-bottom: 1px solid var(--color-surface-tertiary);
}

.card-footer {
    background: var(--color-surface);
    border-top: 1px solid var(--color-surface-tertiary);
}
```

## 3. 暗色主题设计规范

### 3.1 暗色主题色彩定义
**暗色主题色彩系统：**
```css
[data-theme="dark"] {
    /* 品牌色彩 - 提升亮度以保持对比度 */
    --color-primary: #4CC2FF;
    --color-primary-hover: #6BD3FF;
    --color-primary-active: #33B1EE;
    --color-secondary: #8B89E6;
    --color-accent: #FFB347;
    
    /* 功能色彩 - 调整为暗色主题适配 */
    --color-success: #54D454;
    --color-warning: #FFD700;
    --color-error: #FF6B6B;
    --color-info: #4CC2FF;
    
    /* 背景色彩 - 深色系 */
    --color-background: #1F1F1F;
    --color-surface: #2D2D2D;
    --color-surface-secondary: #3A3A3A;
    --color-surface-tertiary: #484848;
    
    /* 文本色彩 - 高对比度 */
    --color-text-primary: #FFFFFF;
    --color-text-secondary: #E1E1E1;
    --color-text-tertiary: #B3B3B3;
    --color-text-disabled: #666666;
    
    /* 边框色彩 */
    --color-border: #484848;
    --color-border-focus: #4CC2FF;
    --color-border-error: #FF6B6B;
    
    /* 阴影 - 增强深度 */
    --shadow-elevation-1: 0 1px 2px rgba(0,0,0,0.4);
    --shadow-elevation-2: 0 2px 4px rgba(0,0,0,0.4);
    --shadow-elevation-4: 0 4px 8px rgba(0,0,0,0.4);
    --shadow-elevation-8: 0 8px 16px rgba(0,0,0,0.4);
    --shadow-elevation-16: 0 16px 32px rgba(0,0,0,0.4);
}
```

### 3.2 暗色主题特殊处理
**图片和图标适配：**
```css
/* 暗色主题下的图标颜色调整 */
[data-theme="dark"] .icon {
    filter: brightness(1.2);
}

/* 暗色主题下的Logo适配 */
[data-theme="dark"] .logo {
    filter: invert(1) brightness(0.8);
}

/* 暗色主题下的图表颜色调整 */
[data-theme="dark"] .chart {
    --chart-grid-color: #484848;
    --chart-text-color: #E1E1E1;
    --chart-background: #2D2D2D;
}
```

**滚动条暗色主题：**
```css
[data-theme="dark"] ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
    background: #2D2D2D;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
    background: #484848;
    border-radius: 4px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background: #5A5A5A;
}
```

## 4. 高对比度主题设计

### 4.1 高对比度主题色彩
**无障碍高对比度色彩：**
```css
[data-theme="high-contrast"] {
    /* 极高对比度色彩 */
    --color-primary: #FFFF00;
    --color-primary-hover: #FFFF66;
    --color-primary-active: #CCCC00;
    --color-secondary: #00FFFF;
    --color-accent: #FF00FF;
    
    /* 功能色彩 */
    --color-success: #00FF00;
    --color-warning: #FFFF00;
    --color-error: #FF0000;
    --color-info: #00FFFF;
    
    /* 背景色彩 */
    --color-background: #000000;
    --color-surface: #000000;
    --color-surface-secondary: #000000;
    --color-surface-tertiary: #333333;
    
    /* 文本色彩 */
    --color-text-primary: #FFFFFF;
    --color-text-secondary: #FFFFFF;
    --color-text-tertiary: #FFFFFF;
    --color-text-disabled: #808080;
    
    /* 边框色彩 */
    --color-border: #FFFFFF;
    --color-border-focus: #FFFF00;
    --color-border-error: #FF0000;
    
    /* 阴影 - 使用白色阴影 */
    --shadow-elevation-1: 0 1px 2px rgba(255,255,255,0.3);
    --shadow-elevation-2: 0 2px 4px rgba(255,255,255,0.3);
    --shadow-elevation-4: 0 4px 8px rgba(255,255,255,0.3);
    --shadow-elevation-8: 0 8px 16px rgba(255,255,255,0.3);
    --shadow-elevation-16: 0 16px 32px rgba(255,255,255,0.3);
}
```

### 4.2 高对比度主题特殊设计
**强化边框和轮廓：**
```css
[data-theme="high-contrast"] .button {
    border: 2px solid var(--color-border);
    outline: 1px solid var(--color-text-primary);
}

[data-theme="high-contrast"] .input {
    border: 2px solid var(--color-border);
    outline: 1px solid var(--color-text-primary);
}

[data-theme="high-contrast"] .card {
    border: 2px solid var(--color-border);
    outline: 1px solid var(--color-text-primary);
}
```

**焦点指示增强：**
```css
[data-theme="high-contrast"] *:focus {
    outline: 3px solid var(--color-primary);
    outline-offset: 2px;
}
```

## 5. 主题切换动画效果

### 5.1 平滑过渡动画
**主题切换过渡：**
```css
/* 全局过渡效果 */
* {
    transition: 
        background-color 250ms cubic-bezier(0.4, 0.0, 0.2, 1),
        color 250ms cubic-bezier(0.4, 0.0, 0.2, 1),
        border-color 250ms cubic-bezier(0.4, 0.0, 0.2, 1),
        box-shadow 250ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

/* 禁用某些元素的过渡 */
.no-transition,
.no-transition * {
    transition: none !important;
}
```

### 5.2 主题切换加载状态
**切换过程指示：**
```css
/* 主题切换加载遮罩 */
.theme-switching-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    pointer-events: none;
    transition: opacity 150ms;
}

.theme-switching-overlay.active {
    opacity: 1;
    pointer-events: all;
}

.theme-switching-content {
    background: var(--color-background);
    padding: 24px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 16px;
    box-shadow: var(--shadow-elevation-8);
}

.theme-switching-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--color-surface-tertiary);
    border-top: 2px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.theme-switching-text {
    font: 14px/20px Segoe UI;
    color: var(--color-text-primary);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
```

## 6. 主题偏好设置

### 6.1 主题设置界面
**设置页面布局：**
```css
/* 主题设置区域 */
.theme-settings {
    background: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
}

.theme-settings-title {
    font: 16px/24px Segoe UI, semibold;
    color: var(--color-text-primary);
    margin-bottom: 16px;
}

/* 主题选项卡片 */
.theme-option-card {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    border: 1px solid var(--color-border);
    border-radius: 8px;
    cursor: pointer;
    transition: all 150ms;
    margin-bottom: 12px;
}

.theme-option-card:hover {
    border-color: var(--color-primary);
    box-shadow: var(--shadow-elevation-2);
}

.theme-option-card.selected {
    border-color: var(--color-primary);
    background: rgba(0, 120, 212, 0.1);
}

/* 主题预览 */
.theme-preview {
    width: 60px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid var(--color-border);
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
}

.theme-preview.light {
    background: linear-gradient(135deg, #FFFFFF 50%, #F3F2F1 50%);
}

.theme-preview.dark {
    background: linear-gradient(135deg, #1F1F1F 50%, #2D2D2D 50%);
}

.theme-preview.high-contrast {
    background: linear-gradient(135deg, #000000 50%, #FFFFFF 50%);
}

.theme-preview.auto {
    background: linear-gradient(135deg, #FFFFFF 25%, #1F1F1F 25%, #1F1F1F 50%, #FFFFFF 50%, #FFFFFF 75%, #1F1F1F 75%);
}

/* 主题信息 */
.theme-info {
    flex: 1;
}

.theme-name {
    font: 14px/20px Segoe UI, semibold;
    color: var(--color-text-primary);
    margin-bottom: 4px;
}

.theme-description {
    font: 12px/16px Segoe UI;
    color: var(--color-text-secondary);
}
```

### 6.2 自动主题切换
**系统主题检测：**
```javascript
// 检测系统主题偏好
function detectSystemTheme() {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        return 'dark';
    }
    return 'light';
}

// 监听系统主题变化
window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
    if (userThemePreference === 'auto') {
        applyTheme(e.matches ? 'dark' : 'light');
    }
});

// 应用主题
function applyTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('theme-preference', theme);
    
    // 通知其他组件主题已变化
    window.dispatchEvent(new CustomEvent('theme-changed', { 
        detail: { theme } 
    }));
}
```

### 6.3 主题持久化存储
**用户偏好保存：**
```javascript
// 主题偏好管理
class ThemeManager {
    constructor() {
        this.currentTheme = this.loadThemePreference();
        this.applyTheme(this.currentTheme);
    }
    
    loadThemePreference() {
        const saved = localStorage.getItem('theme-preference');
        if (saved && ['light', 'dark', 'high-contrast', 'auto'].includes(saved)) {
            return saved;
        }
        return 'auto'; // 默认自动主题
    }
    
    setTheme(theme) {
        this.currentTheme = theme;
        localStorage.setItem('theme-preference', theme);
        
        if (theme === 'auto') {
            const systemTheme = this.detectSystemTheme();
            this.applyTheme(systemTheme);
        } else {
            this.applyTheme(theme);
        }
    }
    
    applyTheme(theme) {
        // 添加切换动画类
        document.body.classList.add('theme-switching');
        
        // 应用新主题
        document.documentElement.setAttribute('data-theme', theme);
        
        // 移除切换动画类
        setTimeout(() => {
            document.body.classList.remove('theme-switching');
        }, 250);
        
        // 触发主题变化事件
        this.onThemeChange(theme);
    }
    
    onThemeChange(theme) {
        // 更新图表颜色
        this.updateChartColors(theme);
        
        // 更新图标颜色
        this.updateIconColors(theme);
        
        // 通知其他组件
        window.dispatchEvent(new CustomEvent('theme-changed', { 
            detail: { theme } 
        }));
    }
}

// 初始化主题管理器
const themeManager = new ThemeManager();
```

## 7. 主题兼容性测试

### 7.1 主题切换测试清单
**功能测试：**
- [ ] 主题切换按钮正常工作
- [ ] 主题偏好正确保存和恢复
- [ ] 自动主题跟随系统设置
- [ ] 所有组件在不同主题下正常显示
- [ ] 图标和图片在暗色主题下正确适配

**视觉测试：**
- [ ] 所有文本在不同主题下对比度符合标准
- [ ] 状态颜色在不同主题下清晰可辨
- [ ] 焦点指示在所有主题下清晰可见
- [ ] 阴影效果在不同主题下适当显示
- [ ] 过渡动画流畅自然

**无障碍测试：**
- [ ] 高对比度主题符合无障碍标准
- [ ] 屏幕阅读器正确识别主题状态
- [ ] 键盘导航在所有主题下正常工作
- [ ] 颜色盲用户能正确识别状态

### 7.2 性能优化
**主题切换性能：**
- 使用CSS变量减少重绘
- 避免不必要的DOM操作
- 合理使用过渡动画
- 优化图片和图标加载
