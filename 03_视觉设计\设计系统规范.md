# AirMonitor 视觉设计系统规范

## 1. 设计系统选择与定制

### 1.1 设计语言基础
**选择的设计系统：** Microsoft Fluent Design System 2.0
**选择理由：**
- 完美适配Windows平台，提供原生体验
- 支持明/暗主题无缝切换
- 专业工具软件的最佳实践
- 丰富的组件库和交互模式
- 优秀的可访问性支持

### 1.2 AirMonitor定制化设计原则
**专业工具导向：**
- **功能优先**：界面设计服务于功能实现，避免过度装饰
- **信息密度**：合理的信息密度，满足专业用户的效率需求
- **状态清晰**：设备状态、连接状态、操作状态一目了然
- **层次分明**：通过视觉层次引导用户关注重点信息

**工业软件特色：**
- **稳定可靠**：视觉设计传达软件的稳定性和可靠性
- **专业严谨**：色彩和排版体现工业软件的专业性
- **效率导向**：减少视觉干扰，提升操作效率
- **安全感知**：通过视觉设计强化安全操作意识

## 2. 色彩系统设计

### 2.1 主色彩定义（明亮主题）
**主色 (Primary)：** #0078D4 (Fluent Blue)
- **使用场景**：主要按钮、链接、选中状态、品牌标识
- **色彩含义**：专业、可靠、科技感
- **变体色阶**：
  - Primary Light: #40E0FF
  - Primary: #0078D4
  - Primary Dark: #005A9E

**次色 (Secondary)：** #6B69D6 (Fluent Purple)
- **使用场景**：次要按钮、辅助信息、图表配色
- **色彩含义**：创新、智能、高级功能
- **变体色阶**：
  - Secondary Light: #8B89E6
  - Secondary: #6B69D6
  - Secondary Dark: #4B49C6

**强调色 (Accent)：** #FF8C00 (工业橙)
- **使用场景**：重要提醒、关键数据、操作确认
- **色彩含义**：注意、重要、工业特色
- **变体色阶**：
  - Accent Light: #FFB347
  - Accent: #FF8C00
  - Accent Dark: #CC7000

### 2.2 功能色彩定义
**成功色 (Success)：** #107C10 (Fluent Green)
- **使用场景**：成功状态、正常运行、连接成功
- **RGB值**：rgb(16, 124, 16)
- **HSL值**：hsl(120, 77%, 27%)

**警告色 (Warning)：** #FFB900 (Fluent Yellow)
- **使用场景**：警告状态、注意事项、参数异常
- **RGB值**：rgb(255, 185, 0)
- **HSL值**：hsl(44, 100%, 50%)

**错误色 (Error)：** #D13438 (Fluent Red)
- **使用场景**：错误状态、故障报警、危险操作
- **RGB值**：rgb(209, 52, 56)
- **HSL值**：hsl(358, 61%, 51%)

**信息色 (Info)：** #0078D4 (Fluent Blue)
- **使用场景**：信息提示、帮助说明、数据展示
- **RGB值**：rgb(0, 120, 212)
- **HSL值**：hsl(206, 100%, 42%)

### 2.3 中性色彩定义（明亮主题）
**文本色彩层级：**
- **文本主色**：#323130 - 主要文本内容、标题
- **文本次色**：#605E5C - 次要文本内容、说明文字
- **文本辅助色**：#8A8886 - 辅助文本、占位符
- **文本禁用色**：#C8C6C4 - 禁用状态文本

**界面色彩层级：**
- **背景主色**：#FFFFFF - 主要背景色
- **背景次色**：#FAF9F8 - 次要背景色、卡片背景
- **背景辅助色**：#F3F2F1 - 辅助背景色、分组背景
- **边框色**：#EDEBE9 - 分割线、边框
- **阴影色**：rgba(0, 0, 0, 0.133) - 卡片阴影、弹窗阴影

### 2.4 暗色主题色彩定义
**主色彩适配：**
- **Primary Dark**: #4CC2FF (明亮蓝色，提升对比度)
- **Secondary Dark**: #8B89E6 (保持紫色，调整亮度)
- **Accent Dark**: #FFB347 (明亮橙色，保持警示性)

**功能色彩适配：**
- **Success Dark**: #54D454 (明亮绿色)
- **Warning Dark**: #FFD700 (明亮黄色)
- **Error Dark**: #FF6B6B (明亮红色)
- **Info Dark**: #4CC2FF (明亮蓝色)

**中性色彩适配：**
- **背景主色**：#1F1F1F - 主要背景色
- **背景次色**：#2D2D2D - 次要背景色
- **背景辅助色**：#3A3A3A - 辅助背景色
- **文本主色**：#FFFFFF - 主要文本
- **文本次色**：#E1E1E1 - 次要文本
- **边框色**：#484848 - 分割线、边框

### 2.5 色彩无障碍验证
**对比度标准：**
- **正文文本**：对比度 ≥ 4.5:1 (WCAG AA标准)
- **大文本**：对比度 ≥ 3:1 (WCAG AA标准)
- **图标和图形**：对比度 ≥ 3:1 (WCAG AA标准)

**色盲友好设计：**
- **不仅依赖颜色**：重要信息同时使用图标、文字、形状
- **红绿色盲考虑**：避免仅用红绿色区分状态
- **色彩模拟测试**：通过Protanopia、Deuteranopia、Tritanopia测试

## 3. 字体系统设计

### 3.1 字体选择策略
**主字体：** Segoe UI (Windows系统字体)
- **选择理由**：Windows原生字体，最佳渲染效果
- **特点**：清晰易读、现代简洁、多语言支持
- **权重支持**：Light(300)、Regular(400)、Semibold(600)、Bold(700)

**备用字体栈：**
```css
font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, 
             "Helvetica Neue", Helvetica, Arial, sans-serif;
```

**等宽字体：** Consolas
- **使用场景**：代码显示、数据监听、协议解析
- **特点**：等宽设计、代码友好、清晰易读
- **备用字体**：Monaco, "Courier New", monospace

### 3.2 字体层级定义
**标题层级：**
- **H1 主标题**：32px/40px, Semibold(600) - 页面主标题
- **H2 区块标题**：24px/32px, Semibold(600) - 功能区块标题
- **H3 小节标题**：20px/28px, Semibold(600) - 小节标题
- **H4 子标题**：16px/24px, Semibold(600) - 子标题

**正文层级：**
- **正文大**：16px/24px, Regular(400) - 重要正文内容
- **正文标准**：14px/20px, Regular(400) - 标准正文内容
- **正文小**：12px/16px, Regular(400) - 辅助说明文字

**功能文本：**
- **按钮文本**：14px/20px, Semibold(600) - 按钮标签
- **标签文本**：12px/16px, Semibold(600) - 表单标签
- **数据文本**：14px/20px, Regular(400) - 数据显示
- **代码文本**：12px/18px, Regular(400), Consolas - 代码和数据

### 3.3 字体使用规范
**可读性优化：**
- **最小字号**：12px（确保在高DPI屏幕上的可读性）
- **行高比例**：1.4-1.6（提升阅读舒适度）
- **字符间距**：正常间距，特殊情况下微调
- **段落间距**：16px-24px（根据内容重要性调整）

**多语言支持：**
- **中文优化**：微软雅黑作为中文显示备选
- **英文优化**：Segoe UI提供最佳英文显示效果
- **数字优化**：等宽数字显示，便于数据对比

## 4. 图标系统设计

### 4.1 图标风格选择
**图标类型：** Fluent UI Icons (线性风格)
- **风格特点**：简洁线性、现代感强、识别度高
- **尺寸规格**：16px, 20px, 24px, 32px, 48px
- **线条粗细**：1.5px (16px图标), 2px (24px及以上图标)

### 4.2 图标分类和使用
**功能图标：**
- **连接管理**：🔌 plug-connected, 🔌 plug-disconnected
- **监控面板**：📊 chart-line, 📈 analytics
- **设备控制**：🎛️ controls, ⚙️ settings
- **数据分析**：📊 chart-multiple, 📈 trending-up
- **故障诊断**：🔧 wrench, 🩺 health

**状态图标：**
- **成功状态**：✅ checkmark-circle (绿色)
- **警告状态**：⚠️ warning (黄色)
- **错误状态**：❌ error-circle (红色)
- **信息状态**：ℹ️ info (蓝色)
- **加载状态**：🔄 spinner (动画)

**导航图标：**
- **首页**：🏠 home
- **设置**：⚙️ settings
- **帮助**：❓ question-circle
- **用户**：👤 person
- **搜索**：🔍 search

### 4.3 图标设计规范
**设计原则：**
- **一致性**：相同功能使用相同图标
- **识别性**：图标含义清晰易懂
- **简洁性**：避免过度复杂的图标设计
- **适配性**：支持明暗主题切换

**技术规范：**
- **格式**：SVG矢量格式
- **颜色**：单色图标，支持主题色彩
- **对齐**：像素对齐，确保清晰显示
- **命名**：语义化命名，便于管理

## 5. 布局和间距系统

### 5.1 网格系统
**基础网格：** 8px网格系统
- **基础单位**：8px
- **常用间距**：8px, 16px, 24px, 32px, 48px, 64px
- **组件间距**：16px (小间距), 24px (中间距), 32px (大间距)

### 5.2 页面布局规范
**主要布局区域：**
- **导航区域**：320px宽度（可折叠至48px）
- **内容区域**：自适应宽度，最小1024px
- **侧边栏**：280px宽度（可选）
- **底部状态栏**：32px高度

**响应式断点：**
- **大屏幕**：≥1920px - 完整布局
- **标准屏幕**：1366px-1919px - 标准布局
- **小屏幕**：1024px-1365px - 紧凑布局
- **最小支持**：1024px - 最小可用布局

### 5.3 组件间距规范
**卡片和面板：**
- **内边距**：24px (大卡片), 16px (小卡片)
- **外边距**：16px (卡片间距)
- **圆角半径**：4px (小圆角), 8px (标准圆角)

**表单元素：**
- **标签间距**：8px (标签与输入框)
- **字段间距**：16px (表单字段间)
- **按钮间距**：8px (按钮组内), 16px (按钮组间)

## 6. 阴影和深度系统

### 6.1 阴影层级定义
**Fluent Design阴影规范：**
- **深度1**：`box-shadow: 0 1px 2px rgba(0,0,0,0.133)` - 按钮悬停
- **深度2**：`box-shadow: 0 2px 4px rgba(0,0,0,0.133)` - 卡片
- **深度4**：`box-shadow: 0 4px 8px rgba(0,0,0,0.133)` - 弹出菜单
- **深度8**：`box-shadow: 0 8px 16px rgba(0,0,0,0.133)` - 对话框
- **深度16**：`box-shadow: 0 16px 32px rgba(0,0,0,0.133)` - 模态窗口

### 6.2 暗色主题阴影适配
**暗色主题阴影：**
- **深度1**：`box-shadow: 0 1px 2px rgba(0,0,0,0.4)`
- **深度2**：`box-shadow: 0 2px 4px rgba(0,0,0,0.4)`
- **深度4**：`box-shadow: 0 4px 8px rgba(0,0,0,0.4)`
- **深度8**：`box-shadow: 0 8px 16px rgba(0,0,0,0.4)`
- **深度16**：`box-shadow: 0 16px 32px rgba(0,0,0,0.4)`

## 7. 动效和过渡

### 7.1 动效原则
**Fluent Motion原则：**
- **自然性**：模拟真实世界的物理运动
- **功能性**：动效服务于功能，不是装饰
- **高效性**：动效时长控制在150-300ms
- **一致性**：相同类型操作使用相同动效

### 7.2 标准动效定义
**缓动函数：**
- **标准缓动**：`cubic-bezier(0.4, 0.0, 0.2, 1)` - 通用过渡
- **减速缓动**：`cubic-bezier(0.0, 0.0, 0.2, 1)` - 进入动画
- **加速缓动**：`cubic-bezier(0.4, 0.0, 1, 1)` - 退出动画
- **弹性缓动**：`cubic-bezier(0.175, 0.885, 0.32, 1.275)` - 确认操作

**动效时长：**
- **微交互**：150ms - 按钮悬停、焦点变化
- **标准过渡**：250ms - 页面切换、面板展开
- **复杂动画**：400ms - 数据加载、状态变化

## 8. License激活界面专项设计

### 8.1 激活界面视觉风格
**设计理念：** 专业、可信、简洁
- **背景设计**：渐变背景体现科技感和专业性
- **卡片设计**：居中卡片布局，突出激活流程
- **色彩运用**：主要使用品牌蓝色，传达可靠性
- **图标设计**：使用盾牌、钥匙等安全相关图标

### 8.2 激活状态视觉反馈
**激活进度指示：**
- **步骤指示器**：清晰显示激活进度（1/3, 2/3, 3/3）
- **状态图标**：成功✅、失败❌、进行中🔄
- **进度条**：动态显示验证进度
- **状态文字**：明确的状态描述和下一步指导

**设备指纹显示：**
- **指纹可视化**：使用等宽字体显示设备指纹
- **分段显示**：将长指纹分段显示，便于阅读
- **复制功能**：提供一键复制设备指纹功能
- **安全提示**：说明设备指纹的作用和安全性

### 8.3 错误处理界面设计
**License错误类型：**
- **文件无效**：红色警告图标 + 详细错误说明
- **设备不匹配**：黄色警告图标 + 重新激活建议
- **License过期**：橙色提醒图标 + 续费联系方式
- **文件损坏**：红色错误图标 + 重新导入指导

**解决方案引导：**
- **分步骤指导**：清晰的问题解决步骤
- **联系支持**：技术支持联系方式和工单系统
- **常见问题**：FAQ链接和自助解决方案
- **重试机制**：便捷的重试和重新激活功能

## 9. 专业工具界面特色设计

### 9.1 数据密集型界面优化
**信息层次设计：**
- **主要数据**：大字号、高对比度显示
- **次要数据**：中等字号、适中对比度
- **辅助信息**：小字号、低对比度
- **状态指示**：颜色编码 + 图标组合

**数据表格优化：**
- **斑马纹背景**：提升大量数据的可读性
- **固定表头**：长表格滚动时保持表头可见
- **列宽自适应**：根据内容自动调整列宽
- **排序指示**：清晰的排序状态和方向指示

### 9.2 实时数据界面设计
**实时更新视觉反馈：**
- **数据闪烁**：新数据到达时的微妙闪烁效果
- **变化高亮**：数据变化时的颜色高亮
- **更新时间戳**：显示最后更新时间
- **连接状态**：实时连接状态的可视化指示

**性能优化设计：**
- **虚拟滚动**：大量数据的高效渲染
- **数据分页**：合理的数据分页和懒加载
- **缓存指示**：显示数据缓存状态
- **刷新控制**：用户可控的数据刷新频率

## 10. 无障碍设计规范

### 10.1 视觉无障碍
**对比度优化：**
- **文本对比度**：确保所有文本达到WCAG AA标准
- **图标对比度**：功能图标与背景对比度≥3:1
- **状态指示**：不仅依赖颜色，同时使用形状和文字
- **焦点指示**：清晰可见的焦点边框

**字体和排版：**
- **最小字号**：12px，确保可读性
- **行高设置**：1.4-1.6倍行高，提升阅读体验
- **字体选择**：优先使用系统字体，确保最佳渲染
- **缩放支持**：支持200%缩放而不影响功能

### 10.2 交互无障碍
**键盘导航：**
- **Tab顺序**：逻辑清晰的Tab导航顺序
- **快捷键**：重要功能提供键盘快捷键
- **焦点管理**：模态窗口的焦点捕获和恢复
- **跳转链接**：提供跳转到主内容的链接

**屏幕阅读器支持：**
- **语义标记**：使用正确的HTML语义标签
- **ARIA标签**：为复杂组件提供ARIA属性
- **状态通知**：重要状态变化的语音通知
- **内容结构**：清晰的标题层级和内容结构
