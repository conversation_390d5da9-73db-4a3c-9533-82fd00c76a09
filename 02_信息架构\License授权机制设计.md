# AirMonitor License授权机制设计

## 1. 授权机制总体架构

### 1.1 离线License系统设计
```
设备指纹生成 → License文件验证 → 权限解析 → 功能模块加载 → 运行时权限控制
```

**核心特点：**
- **完全离线**：无需网络连接，本地验证
- **设备绑定**：基于硬件指纹的设备唯一性验证
- **权限分级**：支持多种用户角色的权限控制
- **安全可靠**：加密License文件，防止篡改

### 1.2 授权流程架构
```
AirMonitor 启动
├── 设备指纹模块
│   ├── 硬件信息采集
│   ├── 指纹算法计算
│   └── 指纹缓存管理
├── License验证模块
│   ├── License文件读取
│   ├── 数字签名验证
│   ├── 设备指纹匹配
│   └── 有效期检查
├── 权限管理模块
│   ├── 角色权限解析
│   ├── 功能模块控制
│   ├── 操作权限验证
│   └── 审计日志记录
└── 安全保护模块
    ├── 反调试保护
    ├── 文件完整性检查
    ├── 运行时保护
    └── 异常处理机制
```

## 2. 设备指纹生成机制

### 2.1 硬件特征采集
**主要硬件特征：**
- **CPU信息**：CPU ID、型号、核心数、频率
- **主板信息**：主板序列号、BIOS版本、制造商
- **硬盘信息**：硬盘序列号、型号、容量
- **网卡信息**：MAC地址、网卡型号
- **内存信息**：内存容量、型号、频率
- **系统信息**：操作系统版本、安装时间

### 2.2 指纹算法设计
```
硬件特征收集 → 特征标准化 → 权重分配 → 哈希计算 → 指纹生成
```

**算法特点：**
- **稳定性**：硬件更换部分组件时指纹保持相对稳定
- **唯一性**：不同设备生成的指纹具有高度唯一性
- **安全性**：使用加密哈希算法，难以逆向
- **容错性**：支持硬件微调时的指纹匹配容错

### 2.3 指纹生成代码示例
```python
def generate_device_fingerprint():
    """生成设备指纹"""
    hardware_info = {
        'cpu_id': get_cpu_id(),
        'motherboard_serial': get_motherboard_serial(),
        'hdd_serial': get_primary_hdd_serial(),
        'mac_address': get_primary_mac_address(),
        'memory_info': get_memory_info(),
        'os_info': get_os_install_info()
    }
    
    # 特征标准化和权重分配
    weighted_features = apply_weights(hardware_info)
    
    # 生成指纹哈希
    fingerprint = sha256_hash(weighted_features)
    
    return fingerprint
```

## 3. License文件结构设计

### 3.1 License文件格式
```json
{
    "license_info": {
        "license_id": "AirMonitor-2024-001",
        "product_name": "AirMonitor",
        "product_version": "1.0.0",
        "issue_date": "2024-01-15",
        "expire_date": "2025-01-15",
        "license_type": "Commercial"
    },
    "device_binding": {
        "device_fingerprint": "a1b2c3d4e5f6...",
        "device_name": "Engineering-PC-001",
        "binding_date": "2024-01-15"
    },
    "user_permissions": {
        "role_type": "system_engineer",
        "enabled_modules": [
            "device_connection",
            "real_time_monitoring", 
            "device_control",
            "data_analysis",
            "parameter_management",
            "fault_diagnosis",
            "test_tools",
            "report_center"
        ],
        "operation_permissions": {
            "read_parameters": true,
            "write_parameters": true,
            "device_control": true,
            "load_control": true,
            "system_config": true,
            "user_management": false
        }
    },
    "security": {
        "digital_signature": "RSA2048签名",
        "checksum": "SHA256校验和",
        "encryption": "AES256加密"
    }
}
```

### 3.2 License加密保护
- **文件加密**：使用AES256对License文件内容加密
- **数字签名**：使用RSA2048对License文件进行数字签名
- **完整性校验**：SHA256校验和验证文件完整性
- **防篡改**：任何修改都会导致验证失败

## 4. 权限角色定义

### 4.1 用户角色权限矩阵

| 功能模块 | 普通用户 | 售后工程师 | 系统工程师 | 软件工程师 |
|---------|---------|-----------|-----------|-----------|
| 设备连接管理 | ✅ 基础 | ✅ 完整 | ✅ 完整 | ✅ 完整 |
| 实时监控面板 | ✅ 简化 | ✅ 专业 | ✅ 高级 | ✅ 完整 |
| 设备控制中心 | ✅ 基础 | ✅ 标准 | ✅ 高级 | ✅ 完整 |
| 数据监听系统 | ❌ | ✅ 标准 | ✅ 高级 | ✅ 完整 |
| 参数管理中心 | ❌ | ✅ 读取 | ✅ 读写 | ✅ 完整 |
| 数据分析工具 | ✅ 基础 | ✅ 标准 | ✅ 高级 | ✅ 完整 |
| 故障诊断助手 | ✅ 查看 | ✅ 完整 | ✅ 完整 | ✅ 完整 |
| 测试验证工具 | ❌ | ✅ 基础 | ✅ 完整 | ✅ 完整 |
| 负载强控管理 | ❌ | ❌ | ✅ 完整 | ✅ 完整 |
| 协作工作空间 | ❌ | ✅ 基础 | ✅ 完整 | ✅ 完整 |
| 报告生成器 | ✅ 基础 | ✅ 标准 | ✅ 完整 | ✅ 完整 |
| 系统配置管理 | ✅ 基础 | ✅ 标准 | ✅ 高级 | ✅ 完整 |

### 4.2 操作权限细分
**普通用户权限：**
- 查看设备状态和基础参数
- 执行预设的安全操作
- 生成基础监控报告
- 接收和查看报警信息

**售后工程师权限：**
- 读取所有运行参数
- 执行标准的调试操作
- 修改非关键参数
- 生成故障诊断报告

**系统工程师权限：**
- 读写所有系统参数
- 执行高级控制操作
- 进行负载强控操作
- 系统配置和优化

**软件工程师权限：**
- 访问原始数据和协议
- 执行所有调试功能
- 系统级配置修改
- API和扩展功能访问

## 5. License激活流程

### 5.1 首次激活流程
```
软件安装 → 启动软件 → 检测License → 激活向导 → 导入License → 设备验证 → 激活成功
```

**详细步骤：**
1. **软件启动**：检测是否存在有效License
2. **激活向导**：显示License激活界面
3. **License导入**：用户选择License文件导入
4. **文件验证**：验证License文件的完整性和签名
5. **设备匹配**：生成设备指纹并与License中的指纹匹配
6. **权限加载**：解析License中的权限配置
7. **激活完成**：保存激活状态，进入主界面

### 5.2 日常启动验证流程
```
软件启动 → License检查 → 设备验证 → 权限加载 → 进入主界面
```

**快速验证：**
- **缓存机制**：缓存设备指纹，避免重复计算
- **增量验证**：只验证关键硬件变化
- **快速启动**：验证通过后快速加载界面
- **异常处理**：验证失败时的降级处理

### 5.3 License更新流程
```
新License导入 → 版本检查 → 权限对比 → 用户确认 → 更新应用 → 重启验证
```

## 6. 安全保护机制

### 6.1 反破解保护
- **代码混淆**：关键代码进行混淆处理
- **反调试**：检测调试器附加，防止逆向分析
- **完整性检查**：运行时检查程序文件完整性
- **虚拟机检测**：检测虚拟机环境，防止分析

### 6.2 License保护
- **文件隐藏**：License文件存储在隐藏位置
- **加密存储**：License内容加密存储
- **访问控制**：限制License文件的访问权限
- **备份机制**：多重备份防止文件丢失

### 6.3 运行时保护
- **权限检查**：每个操作都进行权限验证
- **会话管理**：管理用户会话状态
- **操作审计**：记录所有关键操作
- **异常监控**：监控异常行为和攻击尝试

## 7. 异常处理机制

### 7.1 License异常处理
**常见异常情况：**
- **License文件丢失**：提示重新导入License
- **License过期**：显示过期提示，联系续费
- **设备指纹不匹配**：硬件变化导致的匹配失败
- **License损坏**：文件损坏或被篡改

**处理策略：**
- **优雅降级**：部分功能在异常情况下仍可使用
- **用户引导**：提供清晰的问题解决指导
- **技术支持**：提供技术支持联系方式
- **日志记录**：详细记录异常情况便于排查

### 7.2 硬件变化处理
**硬件变化类型：**
- **轻微变化**：内存升级、外设更换
- **重大变化**：主板更换、CPU升级
- **系统重装**：操作系统重新安装

**处理机制：**
- **容错匹配**：允许一定程度的硬件变化
- **重新激活**：重大变化时需要重新激活
- **备用方案**：提供人工验证的备用激活方式

## 8. License管理工具

### 8.1 License生成工具
**功能特性：**
- **批量生成**：支持批量生成多个License
- **模板管理**：预设不同角色的License模板
- **有效期管理**：灵活设置License有效期
- **设备绑定**：支持预绑定或后绑定设备

### 8.2 License管理界面
**管理功能：**
- **License状态查看**：显示当前License详细信息
- **权限查看**：显示当前用户的权限范围
- **有效期提醒**：License即将过期时的提醒
- **更新功能**：支持License的在线更新

### 8.3 诊断工具
**诊断功能：**
- **设备指纹查看**：显示当前设备的指纹信息
- **License验证测试**：测试License文件的有效性
- **权限测试**：测试各项功能的权限状态
- **日志查看**：查看License相关的操作日志
