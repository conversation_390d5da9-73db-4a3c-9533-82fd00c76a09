# AirMonitor 界面布局设计规范

## 1. 主界面布局架构

### 1.1 整体布局结构
```
┌─────────────────────────────────────────────────────────────┐
│                    标题栏 (Title Bar)                        │
├─────────────┬───────────────────────────────────────────────┤
│             │                顶部工具栏                      │
│             │           (Top Toolbar)                      │
│   主导航栏   ├───────────────────────────────────────────────┤
│             │                面包屑导航                      │
│ (Navigation │           (Breadcrumb)                       │
│    View)    ├───────────────────────────────────────────────┤
│             │                                              │
│             │              主内容区域                       │
│             │           (Main Content)                     │
│             │                                              │
│             │                                              │
├─────────────┼───────────────────────────────────────────────┤
│             │              状态栏 (Status Bar)              │
└─────────────┴───────────────────────────────────────────────┘
```

### 1.2 布局尺寸规范
**主要区域尺寸：**
- **标题栏高度**：32px (Windows标准)
- **导航栏宽度**：320px (展开) / 48px (折叠)
- **顶部工具栏高度**：48px
- **面包屑导航高度**：40px
- **状态栏高度**：24px
- **最小窗口尺寸**：1024x768px
- **推荐窗口尺寸**：1366x768px

### 1.3 响应式布局断点
```css
/* 布局断点定义 */
.layout-container {
    display: grid;
    grid-template-areas: 
        "nav toolbar toolbar"
        "nav breadcrumb breadcrumb"
        "nav content content"
        "nav status status";
    grid-template-columns: 320px 1fr;
    grid-template-rows: 48px 40px 1fr 24px;
    height: 100vh;
}

/* 中等屏幕适配 (1024px - 1365px) */
@media (max-width: 1365px) {
    .layout-container {
        grid-template-columns: 280px 1fr;
    }
    
    .navigation-view {
        width: 280px;
    }
}

/* 小屏幕适配 (1024px以下) */
@media (max-width: 1024px) {
    .layout-container {
        grid-template-columns: 48px 1fr;
    }
    
    .navigation-view {
        width: 48px;
    }
    
    .navigation-view.expanded {
        width: 280px;
        position: absolute;
        z-index: 1000;
        box-shadow: 0 8px 16px rgba(0,0,0,0.133);
    }
}
```

## 2. 导航区域布局

### 2.1 主导航栏布局
```css
/* 导航栏结构 */
.navigation-view {
    grid-area: nav;
    display: flex;
    flex-direction: column;
    background: #F3F2F1;
    border-right: 1px solid #EDEBE9;
}

/* 导航头部 */
.navigation-header {
    height: 48px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    border-bottom: 1px solid #EDEBE9;
    background: #FFFFFF;
}

.navigation-logo {
    width: 24px;
    height: 24px;
    flex-shrink: 0;
}

.navigation-title {
    font: 16px/24px Segoe UI, semibold;
    color: #323130;
    white-space: nowrap;
    overflow: hidden;
}

.navigation-toggle {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-left: auto;
}

.navigation-toggle:hover {
    background: #E1DFDD;
}

/* 导航菜单区域 */
.navigation-menu {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
}

/* 导航分组 */
.navigation-group {
    margin-bottom: 16px;
}

.navigation-group-title {
    padding: 8px 16px 4px;
    font: 12px/16px Segoe UI, semibold;
    color: #605E5C;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 导航底部 */
.navigation-footer {
    padding: 16px;
    border-top: 1px solid #EDEBE9;
    background: #FAF9F8;
}
```

### 2.2 导航项层级设计
```css
/* 一级导航项 */
.navigation-item.level-1 {
    height: 40px;
    margin: 0 8px;
    padding: 0 12px;
    border-radius: 4px;
}

/* 二级导航项 */
.navigation-item.level-2 {
    height: 36px;
    margin: 0 8px 0 24px;
    padding: 0 12px;
    border-radius: 4px;
    font-size: 13px;
}

/* 三级导航项 */
.navigation-item.level-3 {
    height: 32px;
    margin: 0 8px 0 40px;
    padding: 0 12px;
    border-radius: 4px;
    font-size: 12px;
}

/* 展开/折叠指示器 */
.navigation-expand {
    width: 16px;
    height: 16px;
    margin-left: auto;
    transition: transform 150ms;
}

.navigation-item.expanded .navigation-expand {
    transform: rotate(90deg);
}
```

## 3. 内容区域布局

### 3.1 主内容区域结构
```css
/* 主内容容器 */
.main-content {
    grid-area: content;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: #FAF9F8;
}

/* 内容头部 */
.content-header {
    padding: 24px 32px 16px;
    background: #FFFFFF;
    border-bottom: 1px solid #EDEBE9;
    flex-shrink: 0;
}

.content-title {
    font: 24px/32px Segoe UI, semibold;
    color: #323130;
    margin: 0 0 8px;
}

.content-subtitle {
    font: 14px/20px Segoe UI;
    color: #605E5C;
    margin: 0;
}

/* 内容主体 */
.content-body {
    flex: 1;
    overflow: auto;
    padding: 24px 32px;
}

/* 内容底部 */
.content-footer {
    padding: 16px 32px;
    background: #FFFFFF;
    border-top: 1px solid #EDEBE9;
    flex-shrink: 0;
}
```

### 3.2 页面布局模式

#### 3.2.1 仪表板布局 (Dashboard Layout)
```css
/* 仪表板网格 */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 24px;
    padding: 24px;
}

/* 仪表板卡片 */
.dashboard-card {
    background: #FFFFFF;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.133);
    overflow: hidden;
    min-height: 200px;
}

/* 大卡片 (跨2列) */
.dashboard-card.large {
    grid-column: span 2;
}

/* 高卡片 (跨2行) */
.dashboard-card.tall {
    grid-row: span 2;
}
```

#### 3.2.2 表单布局 (Form Layout)
```css
/* 表单容器 */
.form-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 24px;
}

/* 表单分组 */
.form-group {
    margin-bottom: 24px;
}

.form-group-title {
    font: 16px/24px Segoe UI, semibold;
    color: #323130;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #EDEBE9;
}

/* 表单行 */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 16px;
}

.form-row.single {
    grid-template-columns: 1fr;
}

.form-row.triple {
    grid-template-columns: 1fr 1fr 1fr;
}

/* 表单字段 */
.form-field {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.form-field.horizontal {
    flex-direction: row;
    align-items: center;
    gap: 12px;
}

.form-field.horizontal .form-label {
    min-width: 120px;
    margin-bottom: 0;
}
```

#### 3.2.3 数据表格布局 (Table Layout)
```css
/* 表格容器 */
.table-layout {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* 表格工具栏 */
.table-toolbar {
    padding: 16px 24px;
    background: #FFFFFF;
    border-bottom: 1px solid #EDEBE9;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
    flex-shrink: 0;
}

.table-toolbar-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.table-toolbar-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 表格主体 */
.table-main {
    flex: 1;
    overflow: auto;
    background: #FFFFFF;
}

/* 表格分页 */
.table-pagination {
    padding: 16px 24px;
    background: #FFFFFF;
    border-top: 1px solid #EDEBE9;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
}
```

## 4. 工具栏和状态栏布局

### 4.1 顶部工具栏布局
```css
/* 顶部工具栏 */
.top-toolbar {
    grid-area: toolbar;
    height: 48px;
    padding: 0 24px;
    background: #FFFFFF;
    border-bottom: 1px solid #EDEBE9;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
}

/* 工具栏左侧 */
.toolbar-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* 工具栏中间 */
.toolbar-center {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
}

/* 工具栏右侧 */
.toolbar-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 工具栏按钮组 */
.toolbar-button-group {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px;
    background: #F3F2F1;
    border-radius: 4px;
}

.toolbar-button {
    width: 32px;
    height: 32px;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background 150ms;
}

.toolbar-button:hover {
    background: #E1DFDD;
}

.toolbar-button.active {
    background: #0078D4;
    color: #FFFFFF;
}
```

### 4.2 面包屑导航布局
```css
/* 面包屑导航 */
.breadcrumb-nav {
    grid-area: breadcrumb;
    height: 40px;
    padding: 0 24px;
    background: #FFFFFF;
    border-bottom: 1px solid #EDEBE9;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 面包屑项目 */
.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font: 12px/16px Segoe UI;
    color: #605E5C;
    text-decoration: none;
    padding: 4px 8px;
    border-radius: 2px;
    transition: background 150ms;
}

.breadcrumb-item:hover {
    background: #F3F2F1;
    color: #0078D4;
}

.breadcrumb-item.current {
    color: #323130;
    font-weight: 600;
    cursor: default;
}

.breadcrumb-item.current:hover {
    background: transparent;
    color: #323130;
}

/* 面包屑分隔符 */
.breadcrumb-separator {
    color: #8A8886;
    font-size: 10px;
    margin: 0 4px;
}
```

### 4.3 底部状态栏布局
```css
/* 状态栏 */
.status-bar {
    grid-area: status;
    height: 24px;
    padding: 0 16px;
    background: #0078D4;
    color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font: 11px/16px Segoe UI;
}

/* 状态栏左侧 */
.status-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

/* 状态栏右侧 */
.status-right {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* 状态项 */
.status-item {
    display: flex;
    align-items: center;
    gap: 4px;
    white-space: nowrap;
}

.status-icon {
    width: 12px;
    height: 12px;
}

/* 连接状态指示器 */
.connection-status {
    display: flex;
    align-items: center;
    gap: 6px;
}

.connection-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #54D454; /* 连接成功 */
}

.connection-indicator.disconnected {
    background: #FF6B6B; /* 连接断开 */
}

.connection-indicator.connecting {
    background: #FFD700; /* 连接中 */
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}
```

## 5. 弹窗和对话框布局

### 5.1 模态对话框布局
```css
/* 对话框遮罩 */
.dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    animation: dialog-overlay-enter 250ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

@keyframes dialog-overlay-enter {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* 对话框容器 */
.dialog {
    background: #FFFFFF;
    border-radius: 8px;
    box-shadow: 0 16px 32px rgba(0,0,0,0.133);
    max-width: 600px;
    max-height: 80vh;
    width: 90%;
    display: flex;
    flex-direction: column;
    animation: dialog-enter 250ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

@keyframes dialog-enter {
    from { 
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to { 
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* 对话框头部 */
.dialog-header {
    padding: 24px 24px 16px;
    border-bottom: 1px solid #EDEBE9;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.dialog-title {
    font: 20px/28px Segoe UI, semibold;
    color: #323130;
    margin: 0;
}

.dialog-close {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #605E5C;
}

.dialog-close:hover {
    background: #F3F2F1;
    color: #323130;
}

/* 对话框内容 */
.dialog-content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
}

/* 对话框底部 */
.dialog-footer {
    padding: 16px 24px 24px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
    border-top: 1px solid #EDEBE9;
}
```

### 5.2 侧边面板布局
```css
/* 侧边面板 */
.side-panel {
    position: fixed;
    top: 0;
    right: 0;
    width: 400px;
    height: 100vh;
    background: #FFFFFF;
    box-shadow: -4px 0 8px rgba(0,0,0,0.133);
    transform: translateX(100%);
    transition: transform 250ms cubic-bezier(0.4, 0.0, 0.2, 1);
    z-index: 1500;
    display: flex;
    flex-direction: column;
}

.side-panel.open {
    transform: translateX(0);
}

/* 侧边面板头部 */
.side-panel-header {
    height: 56px;
    padding: 0 24px;
    border-bottom: 1px solid #EDEBE9;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
}

.side-panel-title {
    font: 16px/24px Segoe UI, semibold;
    color: #323130;
}

/* 侧边面板内容 */
.side-panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
}

/* 侧边面板底部 */
.side-panel-footer {
    padding: 16px 24px;
    border-top: 1px solid #EDEBE9;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
    flex-shrink: 0;
}
```

## 6. 特殊布局适配

### 6.1 License激活界面布局
```css
/* License激活容器 */
.license-activation {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #0078D4 0%, #6B69D6 100%);
    padding: 24px;
}

/* 激活卡片 */
.activation-card {
    background: #FFFFFF;
    border-radius: 12px;
    box-shadow: 0 16px 32px rgba(0,0,0,0.2);
    width: 100%;
    max-width: 480px;
    overflow: hidden;
}

/* 激活头部 */
.activation-header {
    padding: 32px 32px 24px;
    text-align: center;
    background: #FAF9F8;
}

.activation-logo {
    width: 64px;
    height: 64px;
    margin: 0 auto 16px;
}

.activation-title {
    font: 24px/32px Segoe UI, semibold;
    color: #323130;
    margin: 0 0 8px;
}

.activation-subtitle {
    font: 14px/20px Segoe UI;
    color: #605E5C;
    margin: 0;
}

/* 激活内容 */
.activation-content {
    padding: 32px;
}

/* 激活底部 */
.activation-footer {
    padding: 24px 32px 32px;
    text-align: center;
    border-top: 1px solid #EDEBE9;
}
```

### 6.2 暗色主题布局适配
```css
/* 暗色主题布局 */
[data-theme="dark"] .layout-container {
    background: #1F1F1F;
    color: #FFFFFF;
}

[data-theme="dark"] .navigation-view {
    background: #2D2D2D;
    border-right-color: #484848;
}

[data-theme="dark"] .navigation-header {
    background: #2D2D2D;
    border-bottom-color: #484848;
}

[data-theme="dark"] .top-toolbar {
    background: #2D2D2D;
    border-bottom-color: #484848;
}

[data-theme="dark"] .breadcrumb-nav {
    background: #2D2D2D;
    border-bottom-color: #484848;
}

[data-theme="dark"] .main-content {
    background: #1F1F1F;
}

[data-theme="dark"] .content-header {
    background: #2D2D2D;
    border-bottom-color: #484848;
}

[data-theme="dark"] .content-footer {
    background: #2D2D2D;
    border-top-color: #484848;
}

[data-theme="dark"] .status-bar {
    background: #0078D4; /* 保持品牌色 */
}
```
